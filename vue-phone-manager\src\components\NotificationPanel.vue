<!-- File: src/components/NotificationPanel.vue -->
<template>
  <div v-if="show" class="fixed inset-0 z-50" @click="handleBackdropClick">
    <!-- 背景遮罩 -->
    <div class="fixed inset-0 bg-black bg-opacity-30 transition-opacity"></div>
    
    <!-- 通知面板 -->
    <div class="fixed top-0 right-0 h-full w-80 bg-white shadow-xl transform transition-transform">
      <!-- 头部 -->
      <div class="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50">
        <div class="flex items-center space-x-2">
          <i class="fas fa-bell text-primary-500"></i>
          <h3 class="text-lg font-semibold text-gray-900">近期动态</h3>
          <span v-if="unreadCount > 0" class="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
            {{ unreadCount }}
          </span>
        </div>
        <button @click="closePanel" class="text-gray-400 hover:text-gray-600">
          <i class="fas fa-times text-xl"></i>
        </button>
      </div>

      <!-- 操作按钮 -->
      <div class="p-4 border-b border-gray-100">
        <button
          @click="markAllAsRead"
          :disabled="unreadCount === 0"
          class="w-full px-4 py-2 text-sm bg-primary-500 text-white rounded-lg hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <i class="fas fa-check-double mr-2"></i>
          全部标记为已读
        </button>
      </div>

      <!-- 通知列表 -->
      <div class="flex-1 overflow-y-auto">
        <div v-if="activities.length === 0" class="flex flex-col items-center justify-center py-12 text-gray-500">
          <i class="fas fa-inbox text-4xl mb-4 opacity-50"></i>
          <p>暂无动态消息</p>
        </div>
        
        <div v-else class="divide-y divide-gray-100">
          <div
            v-for="activity in activities"
            :key="activity.id"
            :class="[
              'p-4 hover:bg-gray-50 transition-colors cursor-pointer',
              !activity.isRead ? 'bg-blue-50 border-l-4 border-l-primary-500' : ''
            ]"
            @click="markAsRead(activity)"
          >
            <!-- 活动图标 -->
            <div class="flex items-start space-x-3">
              <div :class="[
                'flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center',
                getActivityIconBg(activity.type)
              ]">
                <i :class="[activity.icon, 'text-white text-sm']"></i>
              </div>
              
              <!-- 活动内容 -->
              <div class="flex-1 min-w-0">
                <div class="flex items-center justify-between">
                  <p :class="[
                    'text-sm font-medium truncate',
                    !activity.isRead ? 'text-gray-900' : 'text-gray-700'
                  ]">
                    {{ activity.title }}
                  </p>
                  <div v-if="!activity.isRead" class="flex-shrink-0 w-2 h-2 bg-primary-500 rounded-full"></div>
                </div>
                <p class="text-xs text-gray-500 mt-1">{{ activity.subtitle }}</p>
                <p class="text-xs text-gray-400 mt-1">{{ activity.time }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部 -->
      <div class="p-4 border-t border-gray-200 bg-gray-50">
        <button
          @click="viewAllActivities"
          class="w-full text-sm text-primary-600 hover:text-primary-700 font-medium"
        >
          查看所有动态
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  activities: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:show', 'mark-read', 'mark-all-read', 'view-all'])

// 计算属性
const unreadCount = computed(() => {
  return props.activities.filter(activity => !activity.isRead).length
})

// 方法
const closePanel = () => {
  emit('update:show', false)
}

const handleBackdropClick = (event) => {
  // 只有点击背景遮罩时才关闭面板
  if (event.target === event.currentTarget) {
    closePanel()
  }
}

const getActivityIconBg = (type) => {
  const bgMap = {
    'in': 'bg-green-500',
    'out': 'bg-blue-500',
    'update': 'bg-orange-500',
    'alert': 'bg-red-500',
    'info': 'bg-gray-500'
  }
  return bgMap[type] || 'bg-gray-500'
}

const markAsRead = (activity) => {
  if (!activity.isRead) {
    emit('mark-read', activity.id)
  }
}

const markAllAsRead = () => {
  emit('mark-all-read')
}

const viewAllActivities = () => {
  emit('view-all')
  closePanel()
}
</script>

<style scoped>
/* 面板滑入动画 */
.notification-panel-enter-active,
.notification-panel-leave-active {
  transition: transform 0.3s ease;
}

.notification-panel-enter-from,
.notification-panel-leave-to {
  transform: translateX(100%);
}

/* 主色调 */
.bg-primary-500 {
  background-color: #4f46e5;
}

.bg-primary-600 {
  background-color: #4338ca;
}

.text-primary-500 {
  color: #4f46e5;
}

.text-primary-600 {
  color: #4f46e5;
}

.text-primary-700 {
  color: #3730a3;
}

.border-l-primary-500 {
  border-left-color: #4f46e5;
}

/* 滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 4px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>
