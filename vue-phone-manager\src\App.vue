<!-- File: src/App.vue -->
<template>
  <div id="app" class="min-h-screen bg-gray-50">
    <!-- 全局加载指示器 -->
    <Transition name="fade">
      <div 
        v-if="isLoading" 
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      >
        <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
          <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500"></div>
          <span class="text-gray-700">加载中...</span>
        </div>
      </div>
    </Transition>

    <!-- 路由视图 -->
    <router-view v-slot="{ Component, route }">
      <Transition name="page" mode="out-in">
        <component :is="Component" :key="route.path" />
      </Transition>
    </router-view>

    <!-- 全局提示消息 -->
    <Transition name="slide-up">
      <div
        v-if="notification.show"
        class="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50"
      >
        <div
          :class="[
            'px-6 py-3 rounded-lg shadow-lg text-white font-medium',
            notification.type === 'success' ? 'bg-green-500' :
            notification.type === 'error' ? 'bg-red-500' :
            notification.type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'
          ]"
        >
          <i
            :class="[
              'mr-2',
              notification.type === 'success' ? 'fas fa-check-circle' :
              notification.type === 'error' ? 'fas fa-exclamation-circle' :
              notification.type === 'warning' ? 'fas fa-exclamation-triangle' : 'fas fa-info-circle'
            ]"
          />
          <span>{{ notification.message }}</span>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'

// 全局状态
const isLoading = ref(false)
const notification = reactive({
  show: false,
  type: 'info', // success, error, warning, info
  message: ''
})

const router = useRouter()

// 全局加载状态管理
const showLoading = () => {
  isLoading.value = true
}

const hideLoading = () => {
  isLoading.value = false
}

// 全局消息提示
const showNotification = (message, type = 'info', duration = 3000) => {
  notification.message = message
  notification.type = type
  notification.show = true
  
  setTimeout(() => {
    notification.show = false
  }, duration)
}

// 监听路由变化，显示加载状态
router.beforeEach((to, from, next) => {
  if (to.path !== from.path) {
    showLoading()
  }
  next()
})

router.afterEach(() => {
  // 延迟隐藏加载状态，确保页面渲染完成
  setTimeout(() => {
    hideLoading()
  }, 300)
})

// 全局错误处理
window.addEventListener('unhandledrejection', (event) => {
  console.error('未处理的Promise错误:', event.reason)
  showNotification('系统错误，请稍后重试', 'error')
})

// 组件挂载时的初始化
onMounted(() => {
  console.log('应用初始化完成')
  
  // 检查浏览器兼容性
  if (!window.fetch) {
    showNotification('您的浏览器版本过低，请升级浏览器', 'warning', 5000)
  }
})

// 暴露全局方法给子组件使用
window.$app = {
  showLoading,
  hideLoading,
  showNotification
}
</script>

<style scoped>
/* 页面切换动画 */
.page-enter-active,
.page-leave-active {
  transition: all 0.3s ease;
}

.page-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.page-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

/* 淡入淡出动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 滑入动画 */
.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.slide-up-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}
</style>
