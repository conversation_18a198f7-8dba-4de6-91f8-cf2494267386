<template>
  <div class="merchant-dashboard pb-20">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm">
      <div class="max-w-md mx-auto px-4 py-4 flex items-center justify-between">
        <div>
          <h1 class="text-xl font-bold text-gray-800">工作台</h1>
          <p class="text-sm text-gray-500">今天是个好日子，加油！</p>
        </div>
        <div class="flex items-center space-x-3">
          <button
            @click="toggleNotificationPanel"
            class="p-2 rounded-full hover:bg-gray-100 transition-colors relative"
          >
            <i class="fas fa-bell text-gray-600 text-lg"></i>
            <span
              v-if="unreadCount > 0"
              class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center"
            >
              {{ unreadCount > 9 ? '9+' : unreadCount }}
            </span>
          </button>
          <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
            <i class="fas fa-user text-white text-sm"></i>
          </div>
        </div>
      </div>
    </header>

    <!-- 加载状态 -->
    <div v-if="loading" class="flex items-center justify-center py-20">
      <div class="text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto mb-4"></div>
        <p class="text-gray-500">加载中...</p>
      </div>
    </div>

    <!-- 工作台内容 -->
    <div v-else class="max-w-md mx-auto px-4 py-4">
      <!-- 核心数据卡片 -->
      <div class="grid grid-cols-3 gap-3 mb-6">
        <div class="stats-card text-white p-4 rounded-xl text-center">
          <div class="stats-number">{{ dashboardData.todayStats?.inStock || 0 }}</div>
          <div class="text-sm opacity-90">今日入库</div>
        </div>
        <div class="stats-card text-white p-4 rounded-xl text-center">
          <div class="stats-number">{{ dashboardData.todayStats?.sold || 0 }}</div>
          <div class="text-sm opacity-90">今日售出</div>
        </div>
        <div class="stats-card text-white p-4 rounded-xl text-center">
          <div class="stats-number">{{ dashboardData.todayStats?.totalInventory || 0 }}</div>
          <div class="text-sm opacity-90">总库存</div>
        </div>
      </div>

      <!-- 智能提醒区 -->
      <div class="mb-6">
        <h2 class="text-lg font-semibold text-gray-800 mb-3">智能提醒</h2>
        <div class="space-y-3">
          <div
            v-for="alert in dashboardData.alerts"
            :key="alert.title"
            :class="[
              'alert-card p-4 rounded-lg border-l-4',
              `alert-${alert.type}`
            ]"
          >
            <div class="flex items-start space-x-3">
              <i :class="[alert.icon, getAlertIconColor(alert.type), 'mt-1']"></i>
              <div>
                <h3 class="font-medium text-gray-800">{{ alert.title }}</h3>
                <p class="text-sm text-gray-600">{{ alert.message }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 近期动态 -->
      <div class="mb-6">
        <h2 class="text-lg font-semibold text-gray-800 mb-3">近期动态</h2>
        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
          <div
            v-for="activity in dashboardData.recentActivity"
            :key="activity.title"
            class="activity-item p-4 border-b border-gray-100 last:border-b-0 flex items-center space-x-3 hover:bg-gray-50 transition-colors"
          >
            <div :class="['activity-icon', `activity-${activity.type}`]">
              <i :class="activity.icon"></i>
            </div>
            <div class="flex-1">
              <p class="text-sm font-medium text-gray-800">{{ activity.title }}</p>
              <p class="text-xs text-gray-500">{{ activity.subtitle }} · {{ activity.time }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 悬浮操作按钮 -->
    <button
      @click="showAddModal"
      class="fab flex items-center justify-center text-white"
    >
      <i class="fas fa-plus text-xl"></i>
    </button>

    <!-- 通知面板 -->
    <NotificationPanel
      v-model:show="showNotificationPanel"
      :activities="dashboardData.recentActivity"
      @mark-read="markNotificationAsRead"
      @mark-all-read="markAllNotificationsAsRead"
      @view-all="viewAllActivities"
    />

    <!-- 入库模态框 -->
    <InventoryFormModal
      v-model:show="showFormModal"
      @submit="handleFormSubmit"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { getDashboardData, markNotificationsAsRead, addInventoryItem } from '@/api/merchant'
import NotificationPanel from '@/components/NotificationPanel.vue'
import InventoryFormModal from '@/components/InventoryFormModal.vue'

// 响应式数据
const loading = ref(false)
const showNotificationPanel = ref(false)
const showFormModal = ref(false)
const dashboardData = ref({
  todayStats: {},
  alerts: [],
  recentActivity: [],
  unreadCount: 0
})

// 计算属性
const unreadCount = computed(() => {
  return dashboardData.value.unreadCount || 0
})

// 方法
const getAlertIconColor = (type) => {
  const colorMap = {
    warning: 'text-orange-600',
    info: 'text-blue-600',
    success: 'text-green-600',
    error: 'text-red-600'
  }
  return colorMap[type] || 'text-gray-600'
}

// 切换通知面板
const toggleNotificationPanel = () => {
  showNotificationPanel.value = !showNotificationPanel.value

  // 打开面板时自动标记所有通知为已读
  if (showNotificationPanel.value && unreadCount.value > 0) {
    setTimeout(() => {
      markAllNotificationsAsRead()
    }, 1000) // 延迟1秒标记为已读，让用户看到未读状态
  }
}

// 显示入库模态框
const showAddModal = () => {
  showFormModal.value = true
}

// 标记单个通知为已读
const markNotificationAsRead = async (notificationId) => {
  try {
    await markNotificationsAsRead([notificationId])

    // 更新本地数据
    const activity = dashboardData.value.recentActivity.find(item => item.id === notificationId)
    if (activity) {
      activity.isRead = true
      dashboardData.value.unreadCount = Math.max(0, dashboardData.value.unreadCount - 1)
    }

  } catch (error) {
    console.error('标记通知失败:', error)
  }
}

// 标记所有通知为已读
const markAllNotificationsAsRead = async () => {
  try {
    await markNotificationsAsRead()

    // 更新本地数据
    dashboardData.value.recentActivity.forEach(activity => {
      activity.isRead = true
    })
    dashboardData.value.unreadCount = 0

    window.$app?.showNotification('所有通知已标记为已读', 'success')

  } catch (error) {
    console.error('标记所有通知失败:', error)
  }
}

// 查看所有动态
const viewAllActivities = () => {
  window.$app?.showNotification('跳转到动态历史页面', 'info')
}

// 处理入库表单提交
const handleFormSubmit = async (formData) => {
  try {
    await addInventoryItem(formData)

    // 添加新的动态到列表顶部
    const newActivity = {
      id: `act_${Date.now()}`,
      type: 'in',
      title: `入库：${formData.title}`,
      subtitle: `入库价: ¥${formData.purchasePrice}`,
      time: '刚刚',
      icon: 'fas fa-plus',
      isRead: false
    }

    dashboardData.value.recentActivity.unshift(newActivity)
    dashboardData.value.unreadCount += 1

    // 更新今日统计
    dashboardData.value.todayStats.inStock += 1
    dashboardData.value.todayStats.totalInventory += 1

    showFormModal.value = false
    window.$app?.showNotification('商品入库成功', 'success')

  } catch (error) {
    console.error('入库失败:', error)
    window.$app?.showNotification('入库失败，请重试', 'error')
  }
}

const loadDashboardData = async () => {
  try {
    loading.value = true
    const data = await getDashboardData()
    dashboardData.value = data
  } catch (error) {
    console.error('加载工作台数据失败:', error)
    window.$app?.showNotification('加载数据失败，请重试', 'error')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadDashboardData()
})
</script>

<style scoped>
/* 数据卡片样式 */
.stats-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
}

.stats-number {
  font-size: 2.5rem;
  font-weight: 800;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 智能提醒卡片 */
.alert-card {
  transition: all 0.3s ease;
}

.alert-card:hover {
  transform: translateX(4px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.alert-warning {
  border-left-color: #f59e0b;
  background: linear-gradient(135deg, #fef3c7, #fde68a);
}

.alert-info {
  border-left-color: #3b82f6;
  background: linear-gradient(135deg, #dbeafe, #bfdbfe);
}

.alert-success {
  border-left-color: #10b981;
  background: linear-gradient(135deg, #d1fae5, #a7f3d0);
}

.alert-error {
  border-left-color: #ef4444;
  background: linear-gradient(135deg, #fee2e2, #fecaca);
}

/* 活动图标样式 */
.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: white;
}

.activity-in {
  background: linear-gradient(135deg, #10b981, #059669);
}

.activity-out {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.activity-update {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
}

/* 悬浮操作按钮 */
.fab {
  position: fixed;
  bottom: 80px;
  right: 20px;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  border-radius: 50%;
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
  transition: all 0.3s ease;
  z-index: 1000;
}

.fab:hover {
  transform: scale(1.1) rotate(90deg);
  box-shadow: 0 12px 35px rgba(255, 107, 107, 0.6);
}
</style>