<!-- File: src/components/InventoryFilterModal.vue -->
<template>
  <!-- 遮罩层 -->
  <Transition name="fade">
    <div 
      v-if="show" 
      class="fixed inset-0 bg-black bg-opacity-50 z-50"
      @click="$emit('update:show', false)"
    >
      <!-- 模态框内容 -->
      <Transition name="slide-up">
        <div 
          v-if="show"
          class="fixed bottom-0 left-0 right-0 bg-white rounded-t-2xl max-h-[80vh] overflow-y-auto"
          @click.stop
        >
          <!-- 头部 -->
          <div class="flex items-center justify-between p-4 border-b">
            <h3 class="text-lg font-semibold text-gray-800">筛选条件</h3>
            <button 
              @click="$emit('update:show', false)"
              class="p-2 rounded-lg hover:bg-gray-100"
            >
              <i class="fas fa-times text-gray-600"></i>
            </button>
          </div>
          
          <!-- 筛选内容 -->
          <div class="p-4 space-y-6">
            <!-- 商品状态筛选 -->
            <div>
              <h4 class="font-medium text-gray-800 mb-3">商品状态</h4>
              <div class="grid grid-cols-2 gap-2">
                <button 
                  @click="localFilters.status = ''"
                  :class="[
                    'p-2 text-sm rounded-lg border transition-colors',
                    !localFilters.status ? 'border-primary-500 bg-primary-50 text-primary-600' : 'border-gray-300 hover:bg-gray-50'
                  ]"
                >
                  全部状态
                </button>
                <button 
                  v-for="status in statusOptions"
                  :key="status.value"
                  @click="localFilters.status = status.value"
                  :class="[
                    'p-2 text-sm rounded-lg border transition-colors',
                    localFilters.status === status.value ? 'border-primary-500 bg-primary-50 text-primary-600' : 'border-gray-300 hover:bg-gray-50'
                  ]"
                >
                  {{ status.label }}
                </button>
              </div>
            </div>
            
            <!-- 品牌筛选 -->
            <div>
              <h4 class="font-medium text-gray-800 mb-3">品牌</h4>
              <div class="grid grid-cols-3 gap-2">
                <button 
                  @click="localFilters.brand = ''"
                  :class="[
                    'p-2 text-sm rounded-lg border transition-colors',
                    !localFilters.brand ? 'border-primary-500 bg-primary-50 text-primary-600' : 'border-gray-300 hover:bg-gray-50'
                  ]"
                >
                  全部品牌
                </button>
                <button 
                  v-for="brand in brands"
                  :key="brand"
                  @click="localFilters.brand = brand"
                  :class="[
                    'p-2 text-sm rounded-lg border transition-colors',
                    localFilters.brand === brand ? 'border-primary-500 bg-primary-50 text-primary-600' : 'border-gray-300 hover:bg-gray-50'
                  ]"
                >
                  {{ brand }}
                </button>
              </div>
            </div>
            
            <!-- 成色筛选 -->
            <div>
              <h4 class="font-medium text-gray-800 mb-3">成色</h4>
              <div class="grid grid-cols-2 gap-2">
                <button 
                  @click="localFilters.condition = null"
                  :class="[
                    'p-2 text-sm rounded-lg border transition-colors',
                    localFilters.condition === null ? 'border-primary-500 bg-primary-50 text-primary-600' : 'border-gray-300 hover:bg-gray-50'
                  ]"
                >
                  不限成色
                </button>
                <button 
                  v-for="condition in conditions"
                  :key="condition.value"
                  @click="localFilters.condition = condition.value"
                  :class="[
                    'p-2 text-sm rounded-lg border transition-colors',
                    localFilters.condition === condition.value ? 'border-primary-500 bg-primary-50 text-primary-600' : 'border-gray-300 hover:bg-gray-50'
                  ]"
                >
                  {{ condition.label }}
                </button>
              </div>
            </div>
          </div>
          
          <!-- 底部操作按钮 -->
          <div class="p-4 border-t bg-gray-50 flex space-x-3">
            <button 
              @click="resetFilters"
              class="flex-1 py-3 px-4 border border-gray-300 rounded-lg text-gray-700 font-medium hover:bg-gray-50 transition-colors"
            >
              重置
            </button>
            <button 
              @click="applyFilters"
              class="flex-1 py-3 px-4 btn-gradient"
            >
              应用筛选
            </button>
          </div>
        </div>
      </Transition>
    </div>
  </Transition>
</template>

<script setup>
import { reactive, watch } from 'vue'

// 组件属性
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  filters: {
    type: Object,
    default: () => ({})
  }
})

// 组件事件
const emit = defineEmits(['update:show', 'apply', 'reset'])

// 本地筛选状态
const localFilters = reactive({
  status: '',
  brand: '',
  condition: null
})

// 状态选项
const statusOptions = [
  { label: '在售', value: 'available' },
  { label: '已预订', value: 'reserved' },
  { label: '已售出', value: 'sold' }
]

// 品牌选项（实际应用中可以从API获取）
const brands = ['Apple', 'Huawei', 'Xiaomi', 'OPPO', 'Vivo', 'Samsung']

// 成色选项
const conditions = [
  { label: '99新以上', value: 99 },
  { label: '95新以上', value: 95 },
  { label: '90新以上', value: 90 },
  { label: '85新以上', value: 85 }
]

// 方法
const applyFilters = () => {
  emit('apply', { ...localFilters })
}

const resetFilters = () => {
  Object.assign(localFilters, {
    status: '',
    brand: '',
    condition: null
  })
  emit('reset')
}

// 监听外部筛选条件变化
watch(() => props.filters, (newFilters) => {
  Object.assign(localFilters, newFilters)
}, { immediate: true, deep: true })
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: transform 0.3s ease;
}

.slide-up-enter-from,
.slide-up-leave-to {
  transform: translateY(100%);
}
</style>
