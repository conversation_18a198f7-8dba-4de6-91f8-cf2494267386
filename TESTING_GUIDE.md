# Vue.js 项目测试指南

## 🎯 测试目标
验证所有页面和功能都能正常工作，没有错误。

## 🚀 启动项目
```bash
cd vue-phone-manager
npm run dev
```
访问: http://localhost:3001

## 📱 功能测试清单

### ✅ 顾客端测试

#### 1. 顾客首页 (`/customer/home`)
- [ ] 页面正常加载，无错误
- [ ] 商品卡片正常显示
- [ ] 搜索功能可以展开/收起
- [ ] 筛选按钮可以点击
- [ ] 商品卡片可以点击跳转详情页
- [ ] 响应式设计在移动端正常

#### 2. 商品详情页 (`/customer/detail/iphone14pro`)
- [ ] 页面正常加载，显示商品信息
- [ ] 图片轮播功能正常
- [ ] 返回按钮可以正常工作
- [ ] 服务承诺区域显示正常
- [ ] 规格参数列表显示完整
- [ ] 底部操作栏显示正常

### ✅ 商家端测试

#### 1. 商家登录页 (`/merchant/login`)
- [ ] 页面正常加载，显示PIN码输入界面
- [ ] 数字键盘可以正常点击
- [ ] 输入PIN码: `123456`
- [ ] 登录成功后跳转到工作台
- [ ] 错误PIN码显示错误提示

#### 2. 商家工作台 (`/merchant/dashboard`)
**前提：需要先登录**
- [ ] 页面正常加载，显示工作台数据
- [ ] 核心数据卡片显示正常
- [ ] 智能提醒区域显示内容
- [ ] 近期动态列表显示正常
- [ ] 悬浮操作按钮可以点击
- [ ] 底部导航栏显示正常

#### 3. 库存管理页 (`/merchant/inventory`)
**前提：需要先登录**
- [ ] 页面正常加载
- [ ] 视图切换按钮（卡片/列表）正常工作
- [ ] 搜索框可以输入
- [ ] 筛选按钮可以点击
- [ ] 库存项目显示正常
- [ ] 编辑/删除按钮可以点击

#### 4. 个人中心页 (`/merchant/profile`)
**前提：需要先登录**
- [ ] 页面正常加载
- [ ] 商家信息卡片显示正常
- [ ] 数据统计区域显示正常
- [ ] 时间周期切换正常
- [ ] 热销排行显示正常
- [ ] 常用工具列表显示正常
- [ ] 退出登录功能正常

### ✅ 路由和导航测试

#### 1. 路由跳转
- [ ] 直接访问 `/` 重定向到 `/customer`
- [ ] 访问 `/customer` 重定向到 `/customer/home`
- [ ] 访问 `/merchant` 重定向到 `/merchant/login`
- [ ] 访问不存在的路由显示404页面

#### 2. 权限控制
- [ ] 未登录访问商家页面自动跳转登录页
- [ ] 已登录访问登录页自动跳转工作台
- [ ] 底部导航只在已登录时显示

#### 3. 底部导航
- [ ] 首页、库存、我的三个标签正常显示
- [ ] 点击标签可以正确跳转
- [ ] 当前页面标签高亮显示

### ✅ 交互功能测试

#### 1. 搜索和筛选
- [ ] 顾客端搜索框输入有防抖效果
- [ ] 筛选模态框可以正常打开/关闭
- [ ] 筛选条件可以正常选择和应用
- [ ] 重置筛选功能正常

#### 2. 数据加载
- [ ] 页面加载时显示loading状态
- [ ] Mock数据正常返回
- [ ] 错误状态有友好提示

#### 3. 响应式设计
- [ ] 在不同屏幕尺寸下布局正常
- [ ] 移动端触摸操作正常
- [ ] 图片和文字缩放适配正常

## 🐛 常见问题排查

### 问题1: 页面显示空白
**解决方案:**
1. 检查浏览器控制台是否有错误
2. 确认所有依赖已正确安装
3. 检查文件路径是否正确

### 问题2: 商家登录失败
**解决方案:**
1. 确认输入的PIN码是 `123456`
2. 检查网络请求是否正常
3. 查看控制台错误信息

### 问题3: 路由跳转异常
**解决方案:**
1. 检查Vue Router配置
2. 确认组件文件存在
3. 检查路由守卫逻辑

### 问题4: 样式显示异常
**解决方案:**
1. 确认Tailwind CSS正常加载
2. 检查Font Awesome图标库
3. 清除浏览器缓存

## 📊 测试结果记录

### 测试环境
- 浏览器: ___________
- 屏幕尺寸: ___________
- 测试时间: ___________

### 测试结果
- 顾客端功能: ⭐⭐⭐⭐⭐
- 商家端功能: ⭐⭐⭐⭐⭐
- 路由导航: ⭐⭐⭐⭐⭐
- 响应式设计: ⭐⭐⭐⭐⭐
- 整体体验: ⭐⭐⭐⭐⭐

### 发现的问题
1. ___________
2. ___________
3. ___________

### 改进建议
1. ___________
2. ___________
3. ___________

## 🎉 测试完成
如果所有测试项目都通过，恭喜！Vue.js项目已经成功运行并且功能完整。
