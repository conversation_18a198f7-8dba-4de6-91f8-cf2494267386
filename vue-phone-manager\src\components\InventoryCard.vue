<!-- File: src/components/InventoryCard.vue -->
<template>
  <div class="inventory-card bg-white rounded-xl p-4 shadow-sm border border-gray-200 hover:border-primary-500 transition-all duration-300">
    <div class="flex space-x-4">
      <img 
        :src="item.image || defaultImage" 
        :alt="item.title"
        class="w-20 h-20 rounded-lg object-cover"
        @error="handleImageError"
      >
      <div class="flex-1">
        <div class="flex items-start justify-between mb-2">
          <h3 class="font-semibold text-gray-800 text-sm line-clamp-2">{{ item.title }}</h3>
          <span :class="['status-badge', statusInfo.class]">
            {{ statusInfo.text }}
          </span>
        </div>
        
        <div class="space-y-1 text-xs text-gray-600">
          <div class="flex justify-between">
            <span>入库价:</span>
            <span class="font-medium">{{ formatPrice(item.purchasePrice) }}</span>
          </div>
          <div class="flex justify-between">
            <span>售价:</span>
            <span class="font-medium text-red-600">{{ formatPrice(item.sellPrice) }}</span>
          </div>
          <div class="flex justify-between">
            <span>{{ getTimeLabel() }}:</span>
            <span class="font-medium">{{ getTimeValue() }}</span>
          </div>
        </div>
        
        <div class="flex items-center justify-between mt-3">
          <div class="flex items-center space-x-2">
            <span v-if="item.condition" :class="['condition-badge', getConditionClass(item.condition)]">
              {{ item.condition }}新
            </span>
            <span v-if="item.batteryHealth" class="condition-badge bg-blue-100 text-blue-800">
              电池{{ item.batteryHealth }}%
            </span>
          </div>
          <div class="flex items-center space-x-2">
            <button 
              @click="$emit('edit', item)"
              class="text-blue-600 text-sm font-medium hover:text-blue-800 transition-colors"
            >
              <i class="fas fa-edit mr-1"></i>编辑
            </button>
            <button 
              @click="$emit('delete', item)"
              class="text-red-600 text-sm font-medium hover:text-red-800 transition-colors"
            >
              <i class="fas fa-trash mr-1"></i>删除
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { formatPrice, getStatusInfo, getTimeAgo } from '@/utils/helpers'

// 组件属性
const props = defineProps({
  item: {
    type: Object,
    required: true
  }
})

// 组件事件
defineEmits(['edit', 'delete'])

// 默认图片
const defaultImage = 'https://placehold.co/80x80/e5e7eb/9ca3af?text=暂无图片'

// 计算属性
const statusInfo = computed(() => {
  return getStatusInfo(props.item.status)
})

// 方法
const handleImageError = (event) => {
  event.target.src = defaultImage
}

const getConditionClass = (condition) => {
  if (condition >= 99) return 'bg-green-100 text-green-800'
  if (condition >= 95) return 'bg-yellow-100 text-yellow-800'
  return 'bg-orange-100 text-orange-800'
}

const getTimeLabel = () => {
  switch (props.item.status) {
    case 'sold':
      return '售出时间'
    case 'reserved':
      return '预订时间'
    default:
      return '库存天数'
  }
}

const getTimeValue = () => {
  switch (props.item.status) {
    case 'sold':
      return props.item.soldAt ? getTimeAgo(props.item.soldAt) : '未知'
    case 'reserved':
      return props.item.reservedAt ? getTimeAgo(props.item.reservedAt) : '未知'
    default:
      return `${props.item.stockDays || 0}天`
  }
}
</script>

<style scoped>
.inventory-card {
  transition: all 0.3s ease;
}

.inventory-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.status-badge {
  @apply text-xs font-semibold px-2 py-1 rounded-full;
}

.condition-badge {
  @apply text-xs font-medium px-2 py-1 rounded;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
