<!-- File: src/components/ConfirmDialog.vue -->
<template>
  <div v-if="show" class="fixed inset-0 z-50 overflow-y-auto">
    <!-- 背景遮罩 -->
    <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity" @click="handleCancel"></div>
    
    <!-- 对话框内容 -->
    <div class="flex min-h-full items-center justify-center p-4">
      <div class="relative bg-white rounded-xl shadow-xl w-full max-w-sm transform transition-all">
        <!-- 图标区域 -->
        <div class="flex items-center justify-center pt-8 pb-4">
          <div :class="[
            'w-16 h-16 rounded-full flex items-center justify-center',
            iconBgClass
          ]">
            <i :class="[iconClass, 'text-2xl text-white']"></i>
          </div>
        </div>

        <!-- 内容区域 -->
        <div class="px-6 pb-6 text-center">
          <h3 class="text-lg font-semibold text-gray-900 mb-2">
            {{ title }}
          </h3>
          <p class="text-gray-600 text-sm leading-relaxed">
            {{ message }}
          </p>
        </div>

        <!-- 按钮区域 -->
        <div class="flex space-x-3 px-6 pb-6">
          <button
            @click="handleCancel"
            class="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            {{ cancelText }}
          </button>
          <button
            @click="handleConfirm"
            :disabled="loading"
            :class="[
              'flex-1 px-4 py-2 rounded-lg text-white transition-colors',
              confirmButtonClass,
              loading ? 'opacity-50 cursor-not-allowed' : ''
            ]"
          >
            <i v-if="loading" class="fas fa-spinner fa-spin mr-2"></i>
            {{ confirmText }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    default: 'warning', // 'warning', 'danger', 'info', 'success'
    validator: (value) => ['warning', 'danger', 'info', 'success'].includes(value)
  },
  title: {
    type: String,
    default: '确认操作'
  },
  message: {
    type: String,
    default: '您确定要执行此操作吗？'
  },
  confirmText: {
    type: String,
    default: '确认'
  },
  cancelText: {
    type: String,
    default: '取消'
  }
})

// Emits
const emit = defineEmits(['update:show', 'confirm', 'cancel'])

// 响应式数据
const loading = ref(false)

// 计算属性
const iconClass = computed(() => {
  const iconMap = {
    warning: 'fas fa-exclamation-triangle',
    danger: 'fas fa-trash-alt',
    info: 'fas fa-info-circle',
    success: 'fas fa-check-circle'
  }
  return iconMap[props.type] || iconMap.warning
})

const iconBgClass = computed(() => {
  const bgMap = {
    warning: 'bg-orange-500',
    danger: 'bg-red-500',
    info: 'bg-blue-500',
    success: 'bg-green-500'
  }
  return bgMap[props.type] || bgMap.warning
})

const confirmButtonClass = computed(() => {
  const buttonMap = {
    warning: 'bg-orange-500 hover:bg-orange-600',
    danger: 'bg-red-500 hover:bg-red-600',
    info: 'bg-blue-500 hover:bg-blue-600',
    success: 'bg-green-500 hover:bg-green-600'
  }
  return buttonMap[props.type] || buttonMap.warning
})

// 方法
const handleCancel = () => {
  if (loading.value) return
  emit('update:show', false)
  emit('cancel')
}

const handleConfirm = async () => {
  try {
    loading.value = true
    await emit('confirm')
    emit('update:show', false)
  } catch (error) {
    console.error('确认操作失败:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
/* 动画效果 */
.dialog-enter-active, .dialog-leave-active {
  transition: all 0.3s ease;
}

.dialog-enter-from, .dialog-leave-to {
  opacity: 0;
  transform: scale(0.9);
}
</style>
