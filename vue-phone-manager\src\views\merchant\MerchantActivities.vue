<!-- File: src/views/merchant/MerchantActivities.vue -->
<template>
  <div class="merchant-activities pb-20">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm">
      <div class="max-w-md mx-auto px-4 py-4 flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <button @click="goBack" class="p-2 rounded-full hover:bg-gray-100 transition-colors">
            <i class="fas fa-arrow-left text-gray-600"></i>
          </button>
          <h1 class="text-xl font-bold text-gray-800">动态历史</h1>
        </div>
        <button @click="markAllAsRead" class="text-sm text-primary-600 hover:text-primary-700 font-medium">
          全部已读
        </button>
      </div>
    </header>

    <!-- 筛选器 -->
    <div class="max-w-md mx-auto px-4 py-4">
      <div class="flex space-x-2 overflow-x-auto">
        <button
          v-for="filter in filters"
          :key="filter.value"
          @click="currentFilter = filter.value"
          :class="[
            'flex-shrink-0 px-4 py-2 rounded-full text-sm font-medium transition-all',
            currentFilter === filter.value
              ? 'bg-primary-500 text-white'
              : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
          ]"
        >
          <i :class="[filter.icon, 'mr-2']"></i>
          {{ filter.label }}
        </button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="flex items-center justify-center py-20">
      <div class="text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto mb-4"></div>
        <p class="text-gray-500">加载中...</p>
      </div>
    </div>

    <!-- 动态列表 -->
    <div v-else class="max-w-md mx-auto px-4">
      <div v-if="filteredActivities.length === 0" class="text-center py-12">
        <i class="fas fa-inbox text-4xl text-gray-300 mb-4"></i>
        <p class="text-gray-500 mb-4">暂无动态记录</p>
        <button @click="resetFilter" class="text-primary-600 hover:text-primary-700 font-medium">
          查看全部
        </button>
      </div>

      <div v-else class="space-y-4">
        <!-- 按日期分组 -->
        <div v-for="group in groupedActivities" :key="group.date" class="space-y-3">
          <!-- 日期标题 -->
          <div class="flex items-center space-x-3">
            <div class="text-sm font-medium text-gray-500">{{ group.date }}</div>
            <div class="flex-1 h-px bg-gray-200"></div>
          </div>

          <!-- 该日期的动态 -->
          <div class="bg-white rounded-xl shadow-sm overflow-hidden">
            <div class="divide-y divide-gray-100">
              <div
                v-for="activity in group.activities"
                :key="activity.id"
                :class="[
                  'p-4 hover:bg-gray-50 transition-colors',
                  !activity.isRead ? 'bg-blue-50 border-l-4 border-l-primary-500' : ''
                ]"
                @click="markAsRead(activity)"
              >
                <div class="flex items-start space-x-3">
                  <!-- 活动图标 -->
                  <div :class="[
                    'flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center',
                    getActivityIconBg(activity.type)
                  ]">
                    <i :class="[activity.icon, 'text-white text-sm']"></i>
                  </div>
                  
                  <!-- 活动内容 -->
                  <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between">
                      <p :class="[
                        'text-sm font-medium',
                        !activity.isRead ? 'text-gray-900' : 'text-gray-700'
                      ]">
                        {{ activity.title }}
                      </p>
                      <div class="flex items-center space-x-2">
                        <span class="text-xs text-gray-400">{{ activity.time }}</span>
                        <div v-if="!activity.isRead" class="w-2 h-2 bg-primary-500 rounded-full"></div>
                      </div>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">{{ activity.subtitle }}</p>
                    
                    <!-- 详细信息 -->
                    <div v-if="activity.details" class="mt-2 text-xs text-gray-400">
                      {{ activity.details }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getDashboardData, markNotificationsAsRead } from '@/api/merchant'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const currentFilter = ref('all')
const activities = ref([])

// 筛选选项
const filters = [
  { label: '全部', value: 'all', icon: 'fas fa-list' },
  { label: '入库', value: 'in', icon: 'fas fa-plus' },
  { label: '售出', value: 'out', icon: 'fas fa-minus' },
  { label: '更新', value: 'update', icon: 'fas fa-edit' },
  { label: '提醒', value: 'alert', icon: 'fas fa-bell' }
]

// 计算属性
const filteredActivities = computed(() => {
  if (currentFilter.value === 'all') {
    return activities.value
  }
  return activities.value.filter(activity => activity.type === currentFilter.value)
})

const groupedActivities = computed(() => {
  const groups = {}
  
  filteredActivities.value.forEach(activity => {
    const date = formatDate(activity.createdAt || new Date())
    if (!groups[date]) {
      groups[date] = {
        date,
        activities: []
      }
    }
    groups[date].activities.push(activity)
  })
  
  return Object.values(groups).sort((a, b) => new Date(b.date) - new Date(a.date))
})

// 方法
const goBack = () => {
  router.go(-1)
}

const resetFilter = () => {
  currentFilter.value = 'all'
}

const formatDate = (dateStr) => {
  const date = new Date(dateStr)
  const today = new Date()
  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)
  
  if (date.toDateString() === today.toDateString()) {
    return '今天'
  } else if (date.toDateString() === yesterday.toDateString()) {
    return '昨天'
  } else {
    return date.toLocaleDateString('zh-CN', { month: 'long', day: 'numeric' })
  }
}

const getActivityIconBg = (type) => {
  const bgMap = {
    'in': 'bg-green-500',
    'out': 'bg-blue-500',
    'update': 'bg-orange-500',
    'alert': 'bg-red-500',
    'info': 'bg-gray-500'
  }
  return bgMap[type] || 'bg-gray-500'
}

const markAsRead = async (activity) => {
  if (!activity.isRead) {
    try {
      await markNotificationsAsRead([activity.id])
      activity.isRead = true
    } catch (error) {
      console.error('标记已读失败:', error)
    }
  }
}

const markAllAsRead = async () => {
  try {
    const unreadIds = activities.value
      .filter(activity => !activity.isRead)
      .map(activity => activity.id)
    
    if (unreadIds.length > 0) {
      await markNotificationsAsRead(unreadIds)
      activities.value.forEach(activity => {
        activity.isRead = true
      })
      window.$app?.showNotification('所有动态已标记为已读', 'success')
    }
  } catch (error) {
    console.error('批量标记已读失败:', error)
    window.$app?.showNotification('操作失败，请重试', 'error')
  }
}

const loadActivities = async () => {
  try {
    loading.value = true
    const data = await getDashboardData()
    
    // 模拟更多历史数据
    const moreActivities = [
      {
        id: 'act006',
        type: 'out',
        title: '售出：OPPO Find X5 Pro 256G',
        subtitle: '售价: ¥2,800',
        time: '2天前',
        icon: 'fas fa-minus',
        isRead: true,
        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        details: '买家：李先生 | 支付方式：微信支付'
      },
      {
        id: 'act007',
        type: 'in',
        title: '入库：三星 Galaxy S23 Ultra 512G',
        subtitle: '入库价: ¥4,200',
        time: '3天前',
        icon: 'fas fa-plus',
        isRead: true,
        createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
        details: '供应商：张先生 | 成色：95新'
      },
      {
        id: 'act008',
        type: 'alert',
        title: '库存预警：iPhone 12 库存不足',
        subtitle: '当前库存：2台',
        time: '4天前',
        icon: 'fas fa-exclamation-triangle',
        isRead: true,
        createdAt: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(),
        details: '建议及时补货以满足市场需求'
      }
    ]
    
    activities.value = [...data.recentActivity, ...moreActivities]
    
  } catch (error) {
    console.error('加载动态失败:', error)
    window.$app?.showNotification('加载失败，请重试', 'error')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadActivities()
})
</script>

<style scoped>
/* 主色调样式 */
.bg-primary-500 {
  background-color: #4f46e5;
}

.text-primary-600 {
  color: #4338ca;
}

.text-primary-700 {
  color: #3730a3;
}

.border-l-primary-500 {
  border-left-color: #4f46e5;
}

/* 滚动条样式 */
.overflow-x-auto::-webkit-scrollbar {
  height: 4px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}
</style>
