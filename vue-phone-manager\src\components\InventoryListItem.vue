<!-- File: src/components/InventoryListItem.vue -->
<template>
  <div class="list-item p-4 flex items-center justify-between hover:bg-gray-50 transition-colors">
    <div class="flex items-center space-x-3 flex-1">
      <img 
        :src="item.image || defaultImage" 
        :alt="item.title"
        class="w-10 h-10 rounded object-cover"
        @error="handleImageError"
      >
      <div class="flex-1 min-w-0">
        <h4 class="font-medium text-gray-800 text-sm truncate">{{ item.title }}</h4>
        <p class="text-xs text-gray-500">
          入库: {{ formatPrice(item.purchasePrice) }} | 
          售价: {{ formatPrice(item.sellPrice) }}
        </p>
        <div class="flex items-center space-x-2 mt-1">
          <span v-if="item.condition" :class="['condition-badge', getConditionClass(item.condition)]">
            {{ item.condition }}新
          </span>
          <span v-if="item.batteryHealth" class="condition-badge bg-blue-100 text-blue-800">
            电池{{ item.batteryHealth }}%
          </span>
        </div>
      </div>
    </div>
    
    <div class="text-right ml-4">
      <span :class="['status-badge', statusInfo.class]">
        {{ statusInfo.text }}
      </span>
      <p class="text-xs text-gray-500 mt-1">{{ getTimeValue() }}</p>
      
      <!-- 操作按钮 -->
      <div class="flex items-center space-x-1 mt-2">
        <button 
          @click="$emit('edit', item)"
          class="text-blue-600 hover:text-blue-800 transition-colors p-1"
          title="编辑"
        >
          <i class="fas fa-edit text-xs"></i>
        </button>
        <button 
          @click="$emit('delete', item)"
          class="text-red-600 hover:text-red-800 transition-colors p-1"
          title="删除"
        >
          <i class="fas fa-trash text-xs"></i>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { formatPrice, getStatusInfo, getTimeAgo } from '@/utils/helpers'

// 组件属性
const props = defineProps({
  item: {
    type: Object,
    required: true
  }
})

// 组件事件
defineEmits(['edit', 'delete'])

// 默认图片
const defaultImage = 'https://placehold.co/40x40/e5e7eb/9ca3af?text=无图'

// 计算属性
const statusInfo = computed(() => {
  return getStatusInfo(props.item.status)
})

// 方法
const handleImageError = (event) => {
  event.target.src = defaultImage
}

const getConditionClass = (condition) => {
  if (condition >= 99) return 'bg-green-100 text-green-800'
  if (condition >= 95) return 'bg-yellow-100 text-yellow-800'
  return 'bg-orange-100 text-orange-800'
}

const getTimeValue = () => {
  switch (props.item.status) {
    case 'sold':
      return props.item.soldAt ? getTimeAgo(props.item.soldAt) : '未知'
    case 'reserved':
      return props.item.reservedAt ? getTimeAgo(props.item.reservedAt) : '未知'
    default:
      return `${props.item.stockDays || 0}天`
  }
}
</script>

<style scoped>
.list-item {
  transition: all 0.2s ease;
}

.list-item:hover {
  transform: translateX(4px);
}

.status-badge {
  @apply text-xs font-semibold px-2 py-1 rounded-full;
}

.condition-badge {
  @apply text-xs font-medium px-2 py-1 rounded;
}
</style>
