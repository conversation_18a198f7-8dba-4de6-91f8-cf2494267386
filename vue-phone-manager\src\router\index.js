// File: src/router/index.js
import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// 路由配置
const routes = [
  {
    path: '/',
    redirect: '/customer'
  },
  // 顾客端路由
  {
    path: '/customer',
    name: 'CustomerLayout',
    component: () => import('@/views/customer/CustomerLayout.vue'),
    children: [
      {
        path: '',
        redirect: '/customer/home'
      },
      {
        path: 'home',
        name: 'CustomerHome',
        component: () => import('@/views/customer/CustomerHome.vue'),
        meta: {
          title: '精品二手机 - 发现好物'
        }
      },
      {
        path: 'detail/:id',
        name: 'CustomerDetail',
        component: () => import('@/views/customer/CustomerDetail.vue'),
        meta: {
          title: '商品详情'
        }
      }
    ]
  },
  // 商家端路由
  {
    path: '/merchant',
    name: 'MerchantLayout',
    component: () => import('@/views/merchant/MerchantLayout.vue'),
    children: [
      {
        path: '',
        redirect: '/merchant/login'
      },
      {
        path: 'login',
        name: 'MerchantLogin',
        component: () => import('@/views/merchant/MerchantLogin.vue'),
        meta: {
          title: '商家登录',
          requiresGuest: true // 需要未登录状态
        }
      },
      {
        path: 'dashboard',
        name: 'MerchantDashboard',
        component: () => import('@/views/merchant/MerchantDashboard.vue'),
        meta: {
          title: '商家工作台',
          requiresAuth: true // 需要登录
        }
      },
      {
        path: 'inventory',
        name: 'MerchantInventory',
        component: () => import('@/views/merchant/MerchantInventory.vue'),
        meta: {
          title: '库存管理',
          requiresAuth: true
        }
      },
      {
        path: 'profile',
        name: 'MerchantProfile',
        component: () => import('@/views/merchant/MerchantProfile.vue'),
        meta: {
          title: '个人中心',
          requiresAuth: true
        }
      },
      {
        path: 'activities',
        name: 'MerchantActivities',
        component: () => import('@/views/merchant/MerchantActivities.vue'),
        meta: {
          title: '动态历史',
          requiresAuth: true
        }
      }
    ]
  },
  // 404页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: {
      title: '页面不存在'
    }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    // 路由切换时的滚动行为
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 全局前置守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = to.meta.title
  }

  // 获取认证状态
  const authStore = useAuthStore()
  const isLoggedIn = authStore.isLoggedIn

  // 检查是否需要登录
  if (to.meta.requiresAuth && !isLoggedIn) {
    console.log('需要登录，重定向到登录页')
    next({
      name: 'MerchantLogin',
      query: { redirect: to.fullPath }
    })
    return
  }

  // 检查是否需要未登录状态（如登录页）
  if (to.meta.requiresGuest && isLoggedIn) {
    console.log('已登录，重定向到工作台')
    next({ name: 'MerchantDashboard' })
    return
  }

  next()
})

// 全局后置钩子
router.afterEach((to, from) => {
  console.log(`路由跳转: ${from.path} -> ${to.path}`)
})

export default router
