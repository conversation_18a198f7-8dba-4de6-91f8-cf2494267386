<!-- File: src/components/InventoryFormModal.vue -->
<template>
  <div v-if="show" class="fixed inset-0 z-50 overflow-y-auto">
    <!-- 背景遮罩 -->
    <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity" @click="closeModal"></div>
    
    <!-- 模态框内容 -->
    <div class="flex min-h-full items-center justify-center p-4">
      <div class="relative bg-white rounded-xl shadow-xl w-full max-w-md transform transition-all">
        <!-- 头部 -->
        <div class="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">
            {{ isEdit ? '编辑库存' : '添加入库' }}
          </h3>
          <button @click="closeModal" class="text-gray-400 hover:text-gray-600">
            <i class="fas fa-times text-xl"></i>
          </button>
        </div>

        <!-- 表单内容 -->
        <form @submit.prevent="handleSubmit" class="p-6 space-y-6">
          <!-- 拍照/上传区域 -->
          <div class="space-y-4">
            <label class="block text-sm font-medium text-gray-700">商品图片</label>
            
            <!-- 图片预览 -->
            <div v-if="formData.imagePreview" class="relative">
              <img 
                :src="formData.imagePreview" 
                alt="商品预览" 
                class="w-full h-48 object-cover rounded-lg border-2 border-gray-200"
              >
              <button
                type="button"
                @click="removeImage"
                class="absolute top-2 right-2 bg-red-500 text-white rounded-full w-8 h-8 flex items-center justify-center hover:bg-red-600"
              >
                <i class="fas fa-times text-sm"></i>
              </button>
            </div>

            <!-- 上传按钮区域 -->
            <div v-else class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
              <i class="fas fa-camera text-4xl text-gray-400 mb-4"></i>
              <p class="text-gray-500 mb-4">添加商品图片</p>
              <div class="flex space-x-3 justify-center">
                <button
                  type="button"
                  @click="openCamera"
                  class="px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors"
                >
                  <i class="fas fa-camera mr-2"></i>拍照
                </button>
                <button
                  type="button"
                  @click="openGallery"
                  class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
                >
                  <i class="fas fa-image mr-2"></i>相册
                </button>
              </div>
            </div>

            <!-- 隐藏的文件输入 -->
            <input
              ref="fileInput"
              type="file"
              accept="image/*"
              capture="environment"
              class="hidden"
              @change="handleFileSelect"
            >
            <input
              ref="galleryInput"
              type="file"
              accept="image/*"
              class="hidden"
              @change="handleFileSelect"
            >
          </div>

          <!-- 基本信息 -->
          <div class="grid grid-cols-1 gap-4">
            <!-- 手机型号 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">手机型号 *</label>
              <input
                v-model="formData.title"
                type="text"
                required
                placeholder="例如：iPhone 14 Pro"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
            </div>

            <!-- 规格和品牌 -->
            <div class="grid grid-cols-2 gap-3">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">规格</label>
                <select
                  v-model="formData.storage"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="">选择容量</option>
                  <option value="64GB">64GB</option>
                  <option value="128GB">128GB</option>
                  <option value="256GB">256GB</option>
                  <option value="512GB">512GB</option>
                  <option value="1TB">1TB</option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">品牌</label>
                <select
                  v-model="formData.brand"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="">选择品牌</option>
                  <option value="Apple">苹果</option>
                  <option value="Huawei">华为</option>
                  <option value="Xiaomi">小米</option>
                  <option value="OPPO">OPPO</option>
                  <option value="Vivo">Vivo</option>
                  <option value="Samsung">三星</option>
                  <option value="OnePlus">一加</option>
                  <option value="Other">其他</option>
                </select>
              </div>
            </div>

            <!-- 成色和电池健康度 -->
            <div class="grid grid-cols-2 gap-3">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">成色 *</label>
                <select
                  v-model="formData.condition"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="">选择成色</option>
                  <option value="99">99新</option>
                  <option value="95">95新</option>
                  <option value="90">9成新</option>
                  <option value="85">8.5成新</option>
                  <option value="80">8成新</option>
                  <option value="75">7.5成新</option>
                  <option value="70">7成新以下</option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">电池健康度</label>
                <input
                  v-model.number="formData.batteryHealth"
                  type="number"
                  min="60"
                  max="100"
                  placeholder="例如：85"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
              </div>
            </div>

            <!-- IMEI -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">IMEI</label>
              <input
                v-model="formData.imei"
                type="text"
                placeholder="设备唯一标识码"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
            </div>

            <!-- 价格信息 -->
            <div class="grid grid-cols-2 gap-3">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">入库价格 *</label>
                <input
                  v-model.number="formData.purchasePrice"
                  type="number"
                  required
                  min="0"
                  step="0.01"
                  placeholder="0.00"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">预期售价</label>
                <input
                  v-model.number="formData.sellPrice"
                  type="number"
                  min="0"
                  step="0.01"
                  placeholder="0.00"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
              </div>
            </div>

            <!-- 备注 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">备注</label>
              <textarea
                v-model="formData.notes"
                rows="3"
                placeholder="商品描述、瑕疵说明等..."
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
              ></textarea>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex space-x-3 pt-4">
            <button
              type="button"
              @click="closeModal"
              class="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              取消
            </button>
            <button
              type="submit"
              :disabled="loading"
              class="flex-1 px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <i v-if="loading" class="fas fa-spinner fa-spin mr-2"></i>
              {{ isEdit ? '保存' : '添加' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'

// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  initialData: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:show', 'submit'])

// 响应式数据
const loading = ref(false)
const fileInput = ref(null)
const galleryInput = ref(null)

// 表单数据
const formData = reactive({
  id: '',
  title: '',
  brand: '',
  storage: '',
  condition: '',
  batteryHealth: 100,
  imei: '',
  purchasePrice: '',
  sellPrice: '',
  notes: '',
  image: null,
  imagePreview: ''
})

// 计算属性
const isEdit = computed(() => !!props.initialData?.id)

// 监听初始数据变化
watch(() => props.initialData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    Object.assign(formData, {
      id: newData.id || '',
      title: newData.title || '',
      brand: newData.brand || '',
      storage: newData.storage || '',
      condition: newData.condition || '',
      batteryHealth: newData.batteryHealth || 100,
      imei: newData.imei || '',
      purchasePrice: newData.purchasePrice || '',
      sellPrice: newData.sellPrice || '',
      notes: newData.notes || '',
      image: newData.image || null,
      imagePreview: newData.image || ''
    })
  }
}, { immediate: true })

// 方法
const closeModal = () => {
  emit('update:show', false)
  resetForm()
}

const resetForm = () => {
  Object.assign(formData, {
    id: '',
    title: '',
    brand: '',
    storage: '',
    condition: '',
    batteryHealth: 100,
    imei: '',
    purchasePrice: '',
    sellPrice: '',
    notes: '',
    image: null,
    imagePreview: ''
  })
}

const openCamera = () => {
  fileInput.value?.click()
}

const openGallery = () => {
  galleryInput.value?.click()
}

const handleFileSelect = (event) => {
  const file = event.target.files[0]
  if (file) {
    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      window.$app?.showNotification('请选择图片文件', 'error')
      return
    }

    // 验证文件大小 (5MB)
    if (file.size > 5 * 1024 * 1024) {
      window.$app?.showNotification('图片大小不能超过5MB', 'error')
      return
    }

    formData.image = file

    // 创建预览
    const reader = new FileReader()
    reader.onload = (e) => {
      formData.imagePreview = e.target.result
    }
    reader.readAsDataURL(file)
  }
}

const removeImage = () => {
  formData.image = null
  formData.imagePreview = ''
  if (fileInput.value) fileInput.value.value = ''
  if (galleryInput.value) galleryInput.value.value = ''
}

const handleSubmit = async () => {
  try {
    loading.value = true

    // 基本验证
    if (!formData.title.trim()) {
      window.$app?.showNotification('请输入手机型号', 'error')
      return
    }

    if (!formData.brand) {
      window.$app?.showNotification('请选择品牌', 'error')
      return
    }

    if (!formData.condition) {
      window.$app?.showNotification('请选择成色', 'error')
      return
    }

    if (!formData.purchasePrice || formData.purchasePrice <= 0) {
      window.$app?.showNotification('请输入有效的入库价格', 'error')
      return
    }

    // 构建提交数据
    const submitData = {
      ...formData,
      productId: formData.id || `product_${Date.now()}`,
      status: 'available',
      stockDays: 0
    }

    // 发送数据
    emit('submit', submitData)
    
    window.$app?.showNotification(
      isEdit.value ? '商品信息更新成功' : '商品入库成功', 
      'success'
    )
    
    closeModal()

  } catch (error) {
    console.error('提交失败:', error)
    window.$app?.showNotification('操作失败，请重试', 'error')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
/* 自定义样式 */
.modal-enter-active, .modal-leave-active {
  transition: opacity 0.3s ease;
}

.modal-enter-from, .modal-leave-to {
  opacity: 0;
}

/* 主色调 */
.bg-primary-500 {
  background-color: #4f46e5;
}

.bg-primary-600 {
  background-color: #4338ca;
}

.text-primary-500 {
  color: #4f46e5;
}

.ring-primary-500 {
  --tw-ring-color: #4f46e5;
}

.focus\:ring-primary-500:focus {
  --tw-ring-color: #4f46e5;
}

.border-primary-500 {
  border-color: #4f46e5;
}
</style>
