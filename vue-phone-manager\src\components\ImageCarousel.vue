<!-- File: src/components/ImageCarousel.vue -->
<template>
  <div class="image-carousel bg-white" style="height: 400px;">
    <div class="carousel-container h-full relative overflow-hidden">
      <!-- 图片轨道 -->
      <div 
        class="carousel-track h-full flex transition-transform duration-300 ease-out"
        :style="{ transform: `translateX(-${currentSlide * 100}%)` }"
        @touchstart="handleTouchStart"
        @touchmove="handleTouchMove"
        @touchend="handleTouchEnd"
      >
        <div 
          v-for="(image, index) in images" 
          :key="index"
          class="carousel-slide min-w-full h-full flex-shrink-0"
        >
          <img 
            :src="image" 
            :alt="`商品图片 ${index + 1}`"
            class="w-full h-full object-cover"
            @error="handleImageError"
          >
        </div>
      </div>

      <!-- 指示器 -->
      <div 
        v-if="images.length > 1"
        class="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2"
      >
        <button
          v-for="(image, index) in images"
          :key="index"
          @click="goToSlide(index)"
          :class="[
            'w-2 h-2 rounded-full transition-all duration-300',
            currentSlide === index ? 'bg-white scale-125' : 'bg-white bg-opacity-50'
          ]"
        ></button>
      </div>

      <!-- 左右切换按钮（桌面端） -->
      <template v-if="images.length > 1">
        <button 
          @click="prevSlide"
          class="absolute left-4 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-black bg-opacity-30 text-white rounded-full flex items-center justify-center hover:bg-opacity-50 transition-all duration-200 hidden md:flex"
        >
          <i class="fas fa-chevron-left"></i>
        </button>
        <button 
          @click="nextSlide"
          class="absolute right-4 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-black bg-opacity-30 text-white rounded-full flex items-center justify-center hover:bg-opacity-50 transition-all duration-200 hidden md:flex"
        >
          <i class="fas fa-chevron-right"></i>
        </button>
      </template>

      <!-- 图片计数器 -->
      <div 
        v-if="images.length > 1"
        class="absolute top-4 right-4 bg-black bg-opacity-50 text-white text-sm px-2 py-1 rounded-full"
      >
        {{ currentSlide + 1 }} / {{ images.length }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

// 组件属性
const props = defineProps({
  images: {
    type: Array,
    default: () => []
  },
  autoPlay: {
    type: Boolean,
    default: false
  },
  autoPlayInterval: {
    type: Number,
    default: 5000
  }
})

// 响应式数据
const currentSlide = ref(0)
const autoPlayTimer = ref(null)
const touchStartX = ref(0)
const touchEndX = ref(0)
const isDragging = ref(false)

// 默认图片
const defaultImage = 'https://placehold.co/400x400/e5e7eb/9ca3af?text=暂无图片'

// 方法
const goToSlide = (index) => {
  if (index >= 0 && index < props.images.length) {
    currentSlide.value = index
  }
}

const nextSlide = () => {
  const nextIndex = currentSlide.value + 1
  if (nextIndex >= props.images.length) {
    currentSlide.value = 0
  } else {
    currentSlide.value = nextIndex
  }
}

const prevSlide = () => {
  const prevIndex = currentSlide.value - 1
  if (prevIndex < 0) {
    currentSlide.value = props.images.length - 1
  } else {
    currentSlide.value = prevIndex
  }
}

// 触摸事件处理
const handleTouchStart = (e) => {
  touchStartX.value = e.touches[0].clientX
  isDragging.value = true
  stopAutoPlay()
}

const handleTouchMove = (e) => {
  if (!isDragging.value) return
  touchEndX.value = e.touches[0].clientX
}

const handleTouchEnd = () => {
  if (!isDragging.value) return
  isDragging.value = false
  
  const diffX = touchStartX.value - touchEndX.value
  const threshold = 50 // 滑动阈值
  
  if (Math.abs(diffX) > threshold) {
    if (diffX > 0) {
      nextSlide()
    } else {
      prevSlide()
    }
  }
  
  if (props.autoPlay) {
    startAutoPlay()
  }
}

// 图片加载错误处理
const handleImageError = (event) => {
  event.target.src = defaultImage
}

// 自动播放
const startAutoPlay = () => {
  if (props.autoPlay && props.images.length > 1) {
    autoPlayTimer.value = setInterval(() => {
      nextSlide()
    }, props.autoPlayInterval)
  }
}

const stopAutoPlay = () => {
  if (autoPlayTimer.value) {
    clearInterval(autoPlayTimer.value)
    autoPlayTimer.value = null
  }
}

// 键盘事件处理
const handleKeydown = (e) => {
  if (e.key === 'ArrowLeft') {
    prevSlide()
  } else if (e.key === 'ArrowRight') {
    nextSlide()
  }
}

// 生命周期
onMounted(() => {
  if (props.autoPlay) {
    startAutoPlay()
  }
  
  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  stopAutoPlay()
  document.removeEventListener('keydown', handleKeydown)
})

// 如果没有图片，使用默认图片
const displayImages = computed(() => {
  return props.images.length > 0 ? props.images : [defaultImage]
})
</script>

<style scoped>
.carousel-container {
  position: relative;
  overflow: hidden;
}

.carousel-track {
  display: flex;
  transition: transform 0.3s ease;
}

.carousel-slide {
  min-width: 100%;
  flex-shrink: 0;
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .carousel-container {
    -webkit-overflow-scrolling: touch;
  }
}
</style>
