<!-- File: src/views/merchant/MerchantProfile.vue -->
<template>
  <div class="merchant-profile pb-20">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm">
      <div class="max-w-md mx-auto px-4 py-4 flex items-center justify-between">
        <h1 class="text-xl font-bold text-gray-800">数据分析</h1>
        <button class="p-2 rounded-full hover:bg-gray-100 transition-colors">
          <i class="fas fa-cog text-gray-600 text-lg"></i>
        </button>
      </div>
    </header>

    <!-- 时间筛选器 -->
    <div class="max-w-md mx-auto px-4 py-4">
      <div class="bg-white rounded-xl shadow-sm p-4 mb-4">
        <div class="flex space-x-2">
          <button
            v-for="period in timePeriods"
            :key="period.value"
            @click="changePeriod(period.value)"
            :class="[
              'flex-1 py-2 px-4 rounded-lg text-sm font-medium transition-all',
              currentPeriod === period.value
                ? 'bg-primary-500 text-white shadow-md'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            ]"
          >
            {{ period.label }}
          </button>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="flex items-center justify-center py-20">
      <div class="text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto mb-4"></div>
        <p class="text-gray-500">加载中...</p>
      </div>
    </div>

    <!-- 数据分析内容 -->
    <div v-else class="max-w-md mx-auto px-4">
      <!-- 核心指标卡片 -->
      <div class="grid grid-cols-3 gap-3 mb-6">
        <div class="bg-white rounded-xl shadow-sm p-4 text-center">
          <div class="text-2xl font-bold text-primary-600 mb-1">
            ¥{{ formatNumber(statsData.totalSales) }}
          </div>
          <div class="text-xs text-gray-500">总销售额</div>
        </div>
        <div class="bg-white rounded-xl shadow-sm p-4 text-center">
          <div class="text-2xl font-bold text-green-600 mb-1">
            {{ statsData.profitMargin }}%
          </div>
          <div class="text-xs text-gray-500">平均利润率</div>
        </div>
        <div class="bg-white rounded-xl shadow-sm p-4 text-center">
          <div class="text-2xl font-bold text-blue-600 mb-1">
            {{ statsData.inventoryTurnover }}天
          </div>
          <div class="text-xs text-gray-500">库存周转</div>
        </div>
      </div>

      <!-- 图表网格布局 -->
      <div class="space-y-6">
        <!-- 销售趋势图 -->
        <div class="bg-white rounded-xl shadow-sm p-4">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">销售趋势</h3>
          <div class="h-64">
            <Line
              :data="salesTrendData"
              :options="chartOptions.line"
            />
          </div>
        </div>

        <!-- 库存构成图 -->
        <div class="bg-white rounded-xl shadow-sm p-4">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">库存构成</h3>
          <div class="h-64">
            <Doughnut
              :data="inventoryDistributionData"
              :options="chartOptions.doughnut"
            />
          </div>
        </div>

        <!-- 畅销机型排行 -->
        <div class="bg-white rounded-xl shadow-sm p-4">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">畅销机型 TOP5</h3>
          <div class="h-64">
            <Bar
              :data="topSellingData"
              :options="chartOptions.bar"
            />
          </div>
        </div>
      </div>

      <!-- 商家信息区 -->
      <div class="profile-card rounded-xl p-6 text-center mt-6 mb-6 text-white">
        <div class="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
          <i class="fas fa-store text-3xl text-white"></i>
        </div>
        <h2 class="text-xl font-bold mb-1">{{ merchantInfo.storeName }}</h2>
        <p class="text-sm opacity-90">店主：{{ merchantInfo.ownerName }} | {{ merchantInfo.status }}</p>
        <div class="flex justify-center space-x-6 mt-4">
          <div class="text-center">
            <div class="text-2xl font-bold">{{ merchantInfo.rating }}</div>
            <div class="text-xs opacity-80">店铺评分</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold">{{ merchantInfo.totalInventory }}</div>
            <div class="text-xs opacity-80">总库存</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold">{{ merchantInfo.businessDays }}</div>
            <div class="text-xs opacity-80">营业天数</div>
          </div>
        </div>
      </div>

      <!-- 数据统计区 -->
      <div class="mb-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-800">数据统计</h3>
          <div class="time-toggle flex bg-gray-100 rounded-lg p-1">
            <button 
              v-for="period in timePeriods"
              :key="period.value"
              @click="currentPeriod = period.value"
              :class="[
                'px-3 py-1 text-xs font-medium rounded-md transition-all',
                currentPeriod === period.value ? 'bg-white text-primary-600 shadow-sm' : 'text-gray-600'
              ]"
            >
              {{ period.label }}
            </button>
          </div>
        </div>

        <!-- 核心数据卡片 -->
        <div class="grid grid-cols-2 gap-4 mb-6">
          <div class="stat-card text-white p-4 rounded-xl text-center">
            <div class="text-2xl font-bold">¥{{ formatNumber(salesStats.totalSales) }}</div>
            <div class="text-sm opacity-90">{{ getPeriodText() }}销售额</div>
            <div class="text-xs opacity-75 mt-1">
              <i class="fas fa-arrow-up mr-1"></i>+15.2%
            </div>
          </div>
          <div class="stat-card text-white p-4 rounded-xl text-center">
            <div class="text-2xl font-bold">¥{{ formatNumber(salesStats.totalProfit) }}</div>
            <div class="text-sm opacity-90">{{ getPeriodText() }}利润</div>
            <div class="text-xs opacity-75 mt-1">
              <i class="fas fa-arrow-up mr-1"></i>+8.7%
            </div>
          </div>
        </div>

        <!-- 热销机型排行 -->
        <div class="bg-white rounded-xl p-4 shadow-sm mb-6">
          <h4 class="font-medium text-gray-800 mb-3">本月热销 Top 5</h4>
          <div class="space-y-3">
            <div 
              v-for="item in topSelling" 
              :key="item.name"
              class="flex items-center justify-between"
            >
              <div class="flex items-center space-x-3">
                <span 
                  :class="[
                    'w-6 h-6 text-white text-xs rounded-full flex items-center justify-center font-bold',
                    getRankColor(item.rank)
                  ]"
                >
                  {{ item.rank }}
                </span>
                <span class="text-sm text-gray-800">{{ item.name }}</span>
              </div>
              <span class="text-sm font-medium text-gray-600">{{ item.count }}台</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 常用工具区 -->
      <div class="mb-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">常用工具</h3>
        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
          <div 
            v-for="tool in tools" 
            :key="tool.name"
            @click="handleToolClick(tool)"
            class="tool-item p-4 border-b border-gray-100 last:border-b-0 flex items-center space-x-3 hover:bg-gray-50 transition-colors cursor-pointer"
          >
            <div :class="['tool-icon', tool.iconBg]">
              <i :class="tool.icon"></i>
            </div>
            <div class="flex-1">
              <h4 class="font-medium text-gray-800">{{ tool.name }}</h4>
              <p class="text-xs text-gray-500">{{ tool.description }}</p>
            </div>
            <i class="fas fa-chevron-right text-gray-400"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { getMerchantProfile, getStatsData } from '@/api/merchant'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
} from 'chart.js'
import { Line, Bar, Doughnut } from 'vue-chartjs'

// 注册Chart.js组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
)

const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const currentPeriod = ref('7d')
const merchantInfo = ref({})
const statsData = ref({
  totalSales: 0,
  totalProfit: 0,
  profitMargin: 0,
  inventoryTurnover: 0,
  salesTrend: [],
  labels: [],
  brandDistribution: { labels: [], data: [], colors: [] },
  topSelling: []
})

// 时间周期选项
const timePeriods = [
  { label: '近7天', value: '7d' },
  { label: '近30天', value: '30d' },
  { label: '本季度', value: '90d' }
]

// 计算属性
const periodLabel = computed(() => {
  const period = timePeriods.find(p => p.value === currentPeriod.value)
  return period ? period.label : '近7天'
})

// 销售趋势图数据
const salesTrendData = computed(() => ({
  labels: statsData.value.labels || [],
  datasets: [
    {
      label: '销售额 (¥)',
      data: statsData.value.salesTrend || [],
      borderColor: '#4f46e5',
      backgroundColor: 'rgba(79, 70, 229, 0.1)',
      tension: 0.4,
      fill: true
    }
  ]
}))

// 库存构成图数据
const inventoryDistributionData = computed(() => ({
  labels: statsData.value.brandDistribution?.labels || [],
  datasets: [
    {
      data: statsData.value.brandDistribution?.data || [],
      backgroundColor: statsData.value.brandDistribution?.colors || [],
      borderWidth: 2,
      borderColor: '#ffffff'
    }
  ]
}))

// 畅销机型排行数据
const topSellingData = computed(() => ({
  labels: (statsData.value.topSelling || []).map(item => item.name),
  datasets: [
    {
      label: '销售额 (¥)',
      data: (statsData.value.topSelling || []).map(item => item.revenue),
      backgroundColor: [
        '#4f46e5',
        '#f59e0b',
        '#10b981',
        '#ef4444',
        '#8b5cf6'
      ],
      borderRadius: 4
    }
  ]
}))

// 图表配置选项
const chartOptions = {
  line: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: function(value) {
            return '¥' + formatNumber(value)
          }
        }
      }
    }
  },
  doughnut: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          padding: 20,
          usePointStyle: true
        }
      }
    }
  },
  bar: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: function(value) {
            return '¥' + formatNumber(value)
          }
        }
      }
    }
  }
}

// 工具列表
const tools = [
  {
    name: '导出报表',
    description: '导出销售和库存数据',
    icon: 'fas fa-download',
    iconBg: 'bg-blue-500',
    action: 'export'
  },
  {
    name: '店铺设置',
    description: '管理店铺信息和配置',
    icon: 'fas fa-store',
    iconBg: 'bg-green-500',
    action: 'settings'
  },
  {
    name: '数据分析',
    description: '查看详细的经营分析',
    icon: 'fas fa-chart-bar',
    iconBg: 'bg-purple-500',
    action: 'analytics'
  },
  {
    name: '帮助中心',
    description: '使用指南和常见问题',
    icon: 'fas fa-question-circle',
    iconBg: 'bg-orange-500',
    action: 'help'
  },
  {
    name: '退出登录',
    description: '安全退出商家系统',
    icon: 'fas fa-sign-out-alt',
    iconBg: 'bg-red-500',
    action: 'logout'
  }
]

// 计算属性
const getPeriodText = () => {
  const periodMap = {
    '7d': '本周',
    '30d': '本月',
    '90d': '本季度'
  }
  return periodMap[currentPeriod.value] || '本周'
}

// 方法
const formatNumber = (num) => {
  if (!num) return '0'
  return num.toLocaleString()
}

const getRankColor = (rank) => {
  const colors = {
    1: 'bg-yellow-500',
    2: 'bg-gray-400',
    3: 'bg-orange-500'
  }
  return colors[rank] || 'bg-blue-500'
}

const handleToolClick = (tool) => {
  switch (tool.action) {
    case 'export':
      window.$app?.showNotification('导出功能开发中', 'info')
      break
    case 'settings':
      window.$app?.showNotification('设置功能开发中', 'info')
      break
    case 'analytics':
      window.$app?.showNotification('分析功能开发中', 'info')
      break
    case 'help':
      window.$app?.showNotification('帮助功能开发中', 'info')
      break
    case 'logout':
      handleLogout()
      break
  }
}

const handleLogout = () => {
  if (confirm('确定要退出登录吗？')) {
    authStore.logout()
    router.push('/merchant/login')
    window.$app?.showNotification('已安全退出', 'success')
  }
}

// 格式化数字显示
const formatNumber = (num) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + '万'
  }
  return num.toLocaleString()
}

// 切换时间周期
const changePeriod = async (period) => {
  if (currentPeriod.value === period) return
  currentPeriod.value = period
  await loadStatsData()
}

// 加载统计数据
const loadStatsData = async () => {
  try {
    loading.value = true
    const data = await getStatsData(currentPeriod.value)
    statsData.value = data
  } catch (error) {
    console.error('加载统计数据失败:', error)
    window.$app?.showNotification('加载数据失败，请重试', 'error')
  } finally {
    loading.value = false
  }
}

const loadData = async () => {
  try {
    loading.value = true

    const [profileData, statsDataResult] = await Promise.all([
      getMerchantProfile(),
      getStatsData(currentPeriod.value)
    ])

    merchantInfo.value = profileData
    statsData.value = statsDataResult

  } catch (error) {
    console.error('加载数据失败:', error)
    window.$app?.showNotification('加载数据失败，请重试', 'error')
  } finally {
    loading.value = false
  }
}

// 监听时间周期变化
watch(currentPeriod, () => {
  loadStatsData()
})

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
/* 商家信息卡片 */
.profile-card {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
}

/* 统计卡片样式 */
.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 30px rgba(102, 126, 234, 0.3);
}

/* 工具图标样式 */
.tool-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
}

.tool-item {
  transition: all 0.2s ease;
}

.tool-item:hover {
  transform: translateX(4px);
}

/* 主色调样式 */
.bg-primary-500 {
  background-color: #4f46e5;
}

.text-primary-500 {
  color: #4f46e5;
}

.text-primary-600 {
  color: #4338ca;
}

.border-primary-500 {
  border-color: #4f46e5;
}
</style>
