<!-- File: src/views/merchant/MerchantInventory.vue -->
<template>
  <div class="merchant-inventory pb-20">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm sticky top-0 z-50">
      <div class="max-w-md mx-auto px-4 py-4">
        <div class="flex items-center justify-between mb-4">
          <h1 class="text-xl font-bold text-gray-800">库存管理</h1>
          <div class="flex items-center space-x-2">
            <!-- 视图切换 -->
            <div class="view-toggle flex">
              <button 
                @click="viewMode = 'card'"
                :class="[
                  'px-3 py-2 text-sm font-medium rounded-l-lg',
                  viewMode === 'card' ? 'bg-primary-500 text-white' : 'bg-gray-100 text-gray-600'
                ]"
              >
                <i class="fas fa-th-large mr-1"></i>卡片
              </button>
              <button 
                @click="viewMode = 'list'"
                :class="[
                  'px-3 py-2 text-sm font-medium rounded-r-lg',
                  viewMode === 'list' ? 'bg-primary-500 text-white' : 'bg-gray-100 text-gray-600'
                ]"
              >
                <i class="fas fa-list mr-1"></i>列表
              </button>
            </div>
            <!-- 筛选按钮 -->
            <button 
              @click="showFilterModal = true"
              class="p-2 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <i class="fas fa-filter text-gray-600"></i>
            </button>
          </div>
        </div>
        
        <!-- 搜索框 -->
        <div class="relative">
          <input 
            v-model="searchKeyword"
            type="text" 
            placeholder="搜索商品型号、品牌..." 
            class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
          <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
        </div>
      </div>
    </header>

    <!-- 库存展示区 -->
    <main class="max-w-md mx-auto px-4 py-4">
      <!-- 加载状态 -->
      <div v-if="loading" class="text-center py-8">
        <div class="inline-flex items-center space-x-2 text-gray-500">
          <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-500"></div>
          <span class="text-sm">正在加载库存...</span>
        </div>
      </div>

      <!-- 卡片视图 -->
      <div v-else-if="viewMode === 'card'" class="grid grid-cols-1 gap-4">
        <InventoryCard
          v-for="item in filteredInventory"
          :key="item.id"
          :item="item"
          @edit="editItem"
          @delete="deleteItem"
          @sell="sellItem"
        />
      </div>

      <!-- 列表视图 -->
      <div v-else-if="viewMode === 'list'" class="bg-white rounded-xl shadow-sm overflow-hidden">
        <div class="divide-y divide-gray-100">
          <InventoryListItem
            v-for="item in filteredInventory"
            :key="item.id"
            :item="item"
            @edit="editItem"
            @delete="deleteItem"
            @sell="sellItem"
          />
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="!loading && filteredInventory.length === 0" class="text-center py-12">
        <i class="fas fa-box-open text-4xl text-gray-300 mb-4"></i>
        <p class="text-gray-500 mb-4">暂无库存商品</p>
        <button 
          @click="resetFilters"
          class="btn-gradient"
        >
          重置筛选
        </button>
      </div>
    </main>

    <!-- 筛选模态框 -->
    <InventoryFilterModal
      v-model:show="showFilterModal"
      :filters="currentFilters"
      @apply="applyFilters"
      @reset="resetFilters"
    />

    <!-- 入库模态框 -->
    <InventoryFormModal
      v-model:show="showFormModal"
      :initial-data="editingItem"
      @submit="handleFormSubmit"
    />

    <!-- 确认删除对话框 -->
    <ConfirmDialog
      v-model:show="showDeleteDialog"
      type="danger"
      title="确认删除"
      :message="`您确定要删除「${deletingItem?.title}」吗？此操作不可撤销。`"
      confirm-text="删除"
      @confirm="confirmDelete"
    />

    <!-- 售出模态框 -->
    <SellModal
      v-model:show="showSellModal"
      :item="sellingItem"
      @confirm="confirmSell"
    />

    <!-- 悬浮添加按钮 -->
    <button
      @click="showAddModal"
      class="fixed bottom-20 right-4 w-14 h-14 bg-primary-500 text-white rounded-full shadow-lg hover:bg-primary-600 transition-all duration-300 flex items-center justify-center z-40"
    >
      <i class="fas fa-plus text-xl"></i>
    </button>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { getInventory, addInventoryItem, updateInventoryItem, deleteInventoryItem, sellInventoryItem } from '@/api/merchant'
import { debounce } from '@/utils/helpers'
import InventoryCard from '@/components/InventoryCard.vue'
import InventoryListItem from '@/components/InventoryListItem.vue'
import InventoryFilterModal from '@/components/InventoryFilterModal.vue'
import InventoryFormModal from '@/components/InventoryFormModal.vue'
import ConfirmDialog from '@/components/ConfirmDialog.vue'
import SellModal from '@/components/SellModal.vue'

// 响应式数据
const loading = ref(false)
const viewMode = ref('card') // 'card' | 'list'
const showFilterModal = ref(false)
const showFormModal = ref(false)
const showDeleteDialog = ref(false)
const showSellModal = ref(false)
const searchKeyword = ref('')
const inventory = ref([])
const editingItem = ref(null)
const deletingItem = ref(null)
const sellingItem = ref(null)
const currentFilters = reactive({
  status: '',
  brand: '',
  condition: null
})

// 计算属性
const filteredInventory = computed(() => {
  let result = inventory.value

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(item => 
      item.title?.toLowerCase().includes(keyword) ||
      item.brand?.toLowerCase().includes(keyword)
    )
  }

  // 按状态筛选
  if (currentFilters.status) {
    result = result.filter(item => item.status === currentFilters.status)
  }

  // 按品牌筛选
  if (currentFilters.brand) {
    result = result.filter(item => item.brand === currentFilters.brand)
  }

  // 按成色筛选
  if (currentFilters.condition) {
    result = result.filter(item => item.condition >= currentFilters.condition)
  }

  return result
})

// 方法
const loadInventory = async () => {
  try {
    loading.value = true
    const data = await getInventory(currentFilters)
    inventory.value = data
  } catch (error) {
    console.error('加载库存失败:', error)
    window.$app?.showNotification('加载库存失败，请重试', 'error')
  } finally {
    loading.value = false
  }
}

const applyFilters = (filters) => {
  Object.assign(currentFilters, filters)
  showFilterModal.value = false
  loadInventory()
}

const resetFilters = () => {
  Object.assign(currentFilters, {
    status: '',
    brand: '',
    condition: null
  })
  searchKeyword.value = ''
  showFilterModal.value = false
  loadInventory()
}

// 显示添加模态框
const showAddModal = () => {
  editingItem.value = null
  showFormModal.value = true
}

// 编辑库存项目
const editItem = (item) => {
  editingItem.value = { ...item }
  showFormModal.value = true
}

// 删除库存项目
const deleteItem = (item) => {
  deletingItem.value = item
  showDeleteDialog.value = true
}

// 售出库存项目
const sellItem = (item) => {
  sellingItem.value = item
  showSellModal.value = true
}

// 处理表单提交
const handleFormSubmit = async (formData) => {
  try {
    if (formData.id && editingItem.value) {
      // 编辑模式
      await updateInventoryItem(formData.id, formData)

      // 更新本地数据
      const index = inventory.value.findIndex(item => item.id === formData.id)
      if (index !== -1) {
        inventory.value[index] = { ...inventory.value[index], ...formData }
      }

      window.$app?.showNotification('商品信息更新成功', 'success')
    } else {
      // 新增模式
      const newItem = await addInventoryItem(formData)

      // 添加到本地数据
      inventory.value.unshift(newItem)

      window.$app?.showNotification('商品入库成功', 'success')
    }

    showFormModal.value = false
    editingItem.value = null

  } catch (error) {
    console.error('操作失败:', error)
    window.$app?.showNotification('操作失败，请重试', 'error')
  }
}

// 确认删除
const confirmDelete = async () => {
  try {
    if (!deletingItem.value) return

    await deleteInventoryItem(deletingItem.value.id)

    // 从本地数据中移除
    const index = inventory.value.findIndex(item => item.id === deletingItem.value.id)
    if (index !== -1) {
      inventory.value.splice(index, 1)
    }

    window.$app?.showNotification('商品删除成功', 'success')

  } catch (error) {
    console.error('删除失败:', error)
    window.$app?.showNotification('删除失败，请重试', 'error')
  } finally {
    deletingItem.value = null
    showDeleteDialog.value = false
  }
}

// 确认售出
const confirmSell = async (sellData) => {
  try {
    if (!sellingItem.value) return

    await sellInventoryItem(sellingItem.value.id, sellData)

    // 更新本地数据
    const index = inventory.value.findIndex(item => item.id === sellingItem.value.id)
    if (index !== -1) {
      inventory.value[index] = {
        ...inventory.value[index],
        status: 'sold',
        actualPrice: sellData.actualPrice,
        buyerInfo: sellData.buyerInfo,
        paymentMethod: sellData.paymentMethod,
        soldAt: sellData.soldAt,
        profit: sellData.profit
      }
    }

    window.$app?.showNotification('商品售出成功', 'success')

  } catch (error) {
    console.error('售出失败:', error)
    window.$app?.showNotification('售出失败，请重试', 'error')
  } finally {
    sellingItem.value = null
    showSellModal.value = false
  }
}

// 防抖搜索
const debouncedSearch = debounce(() => {
  // 搜索逻辑已在计算属性中处理
}, 300)

// 监听搜索关键词变化
watch(searchKeyword, debouncedSearch)

// 组件挂载时加载数据
onMounted(() => {
  loadInventory()
})
</script>

<style scoped>
/* 主色调样式 */
.bg-primary-500 {
  background-color: #4f46e5;
}

.bg-primary-600 {
  background-color: #4338ca;
}

.text-primary-500 {
  color: #4f46e5;
}

.text-primary-600 {
  color: #4338ca;
}

.border-primary-500 {
  border-color: #4f46e5;
}

/* 悬浮按钮样式 */
.fab {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  box-shadow: 0 8px 25px rgba(79, 70, 229, 0.4);
  transition: all 0.3s ease;
}

.fab:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(79, 70, 229, 0.5);
}
.view-toggle {
  background: #f3f4f6;
  border-radius: 8px;
  padding: 2px;
}

.view-toggle button {
  transition: all 0.3s ease;
}
</style>
