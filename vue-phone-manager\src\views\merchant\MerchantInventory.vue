<!-- File: src/views/merchant/MerchantInventory.vue -->
<template>
  <div class="merchant-inventory pb-20">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm sticky top-0 z-50">
      <div class="max-w-md mx-auto px-4 py-4">
        <div class="flex items-center justify-between mb-4">
          <h1 class="text-xl font-bold text-gray-800">库存管理</h1>
          <div class="flex items-center space-x-2">
            <!-- 视图切换 -->
            <div class="view-toggle flex">
              <button 
                @click="viewMode = 'card'"
                :class="[
                  'px-3 py-2 text-sm font-medium rounded-l-lg',
                  viewMode === 'card' ? 'bg-primary-500 text-white' : 'bg-gray-100 text-gray-600'
                ]"
              >
                <i class="fas fa-th-large mr-1"></i>卡片
              </button>
              <button 
                @click="viewMode = 'list'"
                :class="[
                  'px-3 py-2 text-sm font-medium rounded-r-lg',
                  viewMode === 'list' ? 'bg-primary-500 text-white' : 'bg-gray-100 text-gray-600'
                ]"
              >
                <i class="fas fa-list mr-1"></i>列表
              </button>
            </div>
            <!-- 筛选按钮 -->
            <button 
              @click="showFilterModal = true"
              class="p-2 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <i class="fas fa-filter text-gray-600"></i>
            </button>
          </div>
        </div>
        
        <!-- 搜索框 -->
        <div class="relative">
          <input 
            v-model="searchKeyword"
            type="text" 
            placeholder="搜索商品型号、品牌..." 
            class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
          <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
        </div>
      </div>
    </header>

    <!-- 库存展示区 -->
    <main class="max-w-md mx-auto px-4 py-4">
      <!-- 加载状态 -->
      <div v-if="loading" class="text-center py-8">
        <div class="inline-flex items-center space-x-2 text-gray-500">
          <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-500"></div>
          <span class="text-sm">正在加载库存...</span>
        </div>
      </div>

      <!-- 卡片视图 -->
      <div v-else-if="viewMode === 'card'" class="grid grid-cols-1 gap-4">
        <InventoryCard 
          v-for="item in filteredInventory"
          :key="item.id"
          :item="item"
          @edit="editItem"
          @delete="deleteItem"
        />
      </div>

      <!-- 列表视图 -->
      <div v-else-if="viewMode === 'list'" class="bg-white rounded-xl shadow-sm overflow-hidden">
        <div class="divide-y divide-gray-100">
          <InventoryListItem 
            v-for="item in filteredInventory"
            :key="item.id"
            :item="item"
            @edit="editItem"
            @delete="deleteItem"
          />
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="!loading && filteredInventory.length === 0" class="text-center py-12">
        <i class="fas fa-box-open text-4xl text-gray-300 mb-4"></i>
        <p class="text-gray-500 mb-4">暂无库存商品</p>
        <button 
          @click="resetFilters"
          class="btn-gradient"
        >
          重置筛选
        </button>
      </div>
    </main>

    <!-- 筛选模态框 -->
    <InventoryFilterModal 
      v-model:show="showFilterModal"
      :filters="currentFilters"
      @apply="applyFilters"
      @reset="resetFilters"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { getInventory } from '@/api/merchant'
import { debounce } from '@/utils/helpers'
import InventoryCard from '@/components/InventoryCard.vue'
import InventoryListItem from '@/components/InventoryListItem.vue'
import InventoryFilterModal from '@/components/InventoryFilterModal.vue'

// 响应式数据
const loading = ref(false)
const viewMode = ref('card') // 'card' | 'list'
const showFilterModal = ref(false)
const searchKeyword = ref('')
const inventory = ref([])
const currentFilters = reactive({
  status: '',
  brand: '',
  condition: null
})

// 计算属性
const filteredInventory = computed(() => {
  let result = inventory.value

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(item => 
      item.title?.toLowerCase().includes(keyword) ||
      item.brand?.toLowerCase().includes(keyword)
    )
  }

  // 按状态筛选
  if (currentFilters.status) {
    result = result.filter(item => item.status === currentFilters.status)
  }

  // 按品牌筛选
  if (currentFilters.brand) {
    result = result.filter(item => item.brand === currentFilters.brand)
  }

  // 按成色筛选
  if (currentFilters.condition) {
    result = result.filter(item => item.condition >= currentFilters.condition)
  }

  return result
})

// 方法
const loadInventory = async () => {
  try {
    loading.value = true
    const data = await getInventory(currentFilters)
    inventory.value = data
  } catch (error) {
    console.error('加载库存失败:', error)
    window.$app?.showNotification('加载库存失败，请重试', 'error')
  } finally {
    loading.value = false
  }
}

const applyFilters = (filters) => {
  Object.assign(currentFilters, filters)
  showFilterModal.value = false
  loadInventory()
}

const resetFilters = () => {
  Object.assign(currentFilters, {
    status: '',
    brand: '',
    condition: null
  })
  searchKeyword.value = ''
  showFilterModal.value = false
  loadInventory()
}

const editItem = (item) => {
  // 编辑库存项目
  window.$app?.showNotification(`编辑商品: ${item.title}`, 'info')
}

const deleteItem = (item) => {
  // 删除库存项目
  if (confirm(`确定要删除 ${item.title} 吗？`)) {
    window.$app?.showNotification(`已删除: ${item.title}`, 'success')
    loadInventory()
  }
}

// 防抖搜索
const debouncedSearch = debounce(() => {
  // 搜索逻辑已在计算属性中处理
}, 300)

// 监听搜索关键词变化
watch(searchKeyword, debouncedSearch)

// 组件挂载时加载数据
onMounted(() => {
  loadInventory()
})
</script>

<style scoped>
.view-toggle {
  background: #f3f4f6;
  border-radius: 8px;
  padding: 2px;
}

.view-toggle button {
  transition: all 0.3s ease;
}
</style>
