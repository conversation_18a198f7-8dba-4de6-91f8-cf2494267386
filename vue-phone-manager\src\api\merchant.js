// File: src/api/merchant.js
import request from '@/utils/request'

// Mock数据
const mockDashboardData = {
  todayStats: {
    inStock: 3,
    sold: 1,
    totalInventory: 128
  },
  alerts: [
    {
      type: 'warning',
      title: '库存预警',
      message: 'iPhone 12 Pro 仅剩 1 台，建议及时补货',
      icon: 'fas fa-exclamation-triangle'
    },
    {
      type: 'info',
      title: '热门机型',
      message: '最近7天 iPhone 14 Pro 咨询量最高',
      icon: 'fas fa-chart-line'
    },
    {
      type: 'success',
      title: '长期未售出',
      message: '华为 Mate 40 Pro 已在库 60 天，考虑调价',
      icon: 'fas fa-clock'
    }
  ],
  recentActivity: [
    {
      id: 'act001',
      type: 'in',
      title: '入库：iPhone 14 Pro 256G 紫色',
      subtitle: '入库价: ¥5,500',
      time: '2小时前',
      icon: 'fas fa-plus',
      isRead: false
    },
    {
      id: 'act002',
      type: 'out',
      title: '售出：小米 13 Ultra 512G',
      subtitle: '售价: ¥4,800',
      time: '4小时前',
      icon: 'fas fa-minus',
      isRead: false
    },
    {
      id: 'act003',
      type: 'update',
      title: '信息更新：iPhone 13 的参考售价已更新',
      subtitle: '系统自动更新',
      time: '6小时前',
      icon: 'fas fa-edit',
      isRead: true
    },
    {
      id: 'act004',
      type: 'in',
      title: '入库：华为 P50 Pro 256G 白色',
      subtitle: '入库价: ¥3,200',
      time: '1天前',
      icon: 'fas fa-plus',
      isRead: true
    },
    {
      id: 'act005',
      type: 'out',
      title: '售出：iPhone 13 128G 蓝色',
      subtitle: '售价: ¥3,800',
      time: '1天前',
      icon: 'fas fa-minus',
      isRead: true
    }
  ],
  unreadCount: 2
}

const mockInventoryData = [
  {
    id: 'inv001',
    productId: 'iphone14pro',
    title: 'iPhone 14 Pro 256GB 深空黑',
    brand: 'Apple',
    condition: 99,
    batteryHealth: 100,
    purchasePrice: 5500,
    sellPrice: 6299,
    status: 'available',
    stockDays: 3,
    image: 'https://placehold.co/80x80/4f46e5/ffffff?text=iPhone+14',
    createdAt: '2024-06-28T10:00:00Z'
  },
  {
    id: 'inv002',
    productId: 'huaweimate50',
    title: '华为 Mate 50 Pro 512GB',
    brand: 'Huawei',
    condition: 95,
    batteryHealth: 95,
    purchasePrice: 3800,
    sellPrice: 4599,
    status: 'reserved',
    stockDays: 15,
    image: 'https://placehold.co/80x80/f59e0b/ffffff?text=Mate+50',
    createdAt: '2024-06-15T14:30:00Z'
  },
  {
    id: 'inv003',
    productId: 'xiaomi13ultra',
    title: '小米 13 Ultra 512GB 橄榄绿',
    brand: 'Xiaomi',
    condition: 98,
    batteryHealth: 98,
    purchasePrice: 3500,
    sellPrice: 4299,
    status: 'sold',
    stockDays: 0,
    image: 'https://placehold.co/80x80/10b981/ffffff?text=小米13',
    soldAt: '2024-06-30T16:00:00Z',
    createdAt: '2024-06-20T09:15:00Z'
  }
  ,
  {
    id: 'inv003',
    productId: 'xiaomi13ultra11',
    title: '小米 13 Ultra 512GB 橄榄绿',
    brand: 'Xiaomi',
    condition: 98,
    batteryHealth: 98,
    purchasePrice: 3500,
    sellPrice: 4299,
    status: 'sold',
    stockDays: 0,
    image: 'https://placehold.co/80x80/10b981/ffffff?text=小米13',
    soldAt: '2024-06-30T16:00:00Z',
    createdAt: '2024-06-20T09:15:00Z'
  }
]

const mockSalesStats = {
  '7d': {
    totalSales: 12580,
    totalProfit: 3240,
    profitMargin: 25.7,
    inventoryTurnover: 12,
    salesTrend: [1200, 1900, 800, 2100, 1600, 2400, 1800],
    labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
    brandDistribution: {
      labels: ['苹果', '华为', '小米', 'OPPO', '其他'],
      data: [45, 25, 15, 10, 5],
      colors: ['#4f46e5', '#f59e0b', '#10b981', '#ef4444', '#6b7280']
    },
    topSelling: [
      { name: 'iPhone 14 Pro', sales: 8, revenue: 50400, rank: 1 },
      { name: '小米 13 Ultra', sales: 6, revenue: 25800, rank: 2 },
      { name: '华为 P50 Pro', sales: 5, revenue: 22950, rank: 3 },
      { name: 'iPhone 13', sales: 4, revenue: 19600, rank: 4 },
      { name: 'OPPO Find X5', sales: 3, revenue: 11700, rank: 5 }
    ]
  },
  '30d': {
    totalSales: 45600,
    totalProfit: 12800,
    profitMargin: 28.1,
    inventoryTurnover: 8,
    salesTrend: [8000, 9200, 7800, 11000, 10500, 12800, 11200, 9800],
    labels: ['第1周', '第2周', '第3周', '第4周', '第5周', '第6周', '第7周', '第8周'],
    brandDistribution: {
      labels: ['苹果', '华为', '小米', 'OPPO', '其他'],
      data: [42, 28, 18, 8, 4],
      colors: ['#4f46e5', '#f59e0b', '#10b981', '#ef4444', '#6b7280']
    },
    topSelling: [
      { name: 'iPhone 14 Pro', sales: 28, revenue: 176400, rank: 1 },
      { name: '华为 P50 Pro', sales: 22, revenue: 100980, rank: 2 },
      { name: '小米 13 Ultra', sales: 18, revenue: 77400, rank: 3 },
      { name: 'iPhone 13', sales: 15, revenue: 73500, rank: 4 },
      { name: 'OPPO Find X5', sales: 12, revenue: 46800, rank: 5 }
    ]
  },
  '90d': {
    totalSales: 156800,
    totalProfit: 45200,
    profitMargin: 28.8,
    inventoryTurnover: 6,
    salesTrend: [18000, 22000, 19500, 25000, 21000, 28000, 24500, 19800, 23200, 26500, 22800, 25600],
    labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
    brandDistribution: {
      labels: ['苹果', '华为', '小米', 'OPPO', '其他'],
      data: [48, 22, 16, 9, 5],
      colors: ['#4f46e5', '#f59e0b', '#10b981', '#ef4444', '#6b7280']
    },
    topSelling: [
      { name: 'iPhone 14 Pro', sales: 85, revenue: 535500, rank: 1 },
      { name: '华为 P50 Pro', sales: 68, revenue: 312040, rank: 2 },
      { name: '小米 13 Ultra', sales: 52, revenue: 223600, rank: 3 },
      { name: 'iPhone 13', sales: 45, revenue: 220500, rank: 4 },
      { name: 'OPPO Find X5', sales: 38, revenue: 148200, rank: 5 }
    ]
  }
}

const mockMerchantProfile = {
  storeName: '精品二手机专营店',
  ownerName: '张先生',
  status: '营业中',
  rating: 4.9,
  totalInventory: 128,
  businessDays: 365,
  phone: '138****8888',
  address: '北京市朝阳区xxx街道xxx号',
  businessHours: '09:00-21:00'
}

const delay = (ms = 500) => new Promise(resolve => setTimeout(resolve, ms))

/**
 * 商家登录验证
 * @param {string} pinCode - PIN码
 * @returns {Promise<Object>} 登录结果
 */
export async function loginByPin(pinCode) {
  try {
    // 在实际项目中调用真实API
    // return await request.post('/merchant/login', { pinCode })
    
    await delay()
    
    const correctPin = '123456'
    
    if (pinCode === correctPin) {
      const result = {
        token: 'mock-jwt-token-12345',
        merchant: mockMerchantProfile
      }
      
      console.log('商家登录成功')
      return result
    } else {
      throw new Error('PIN码错误')
    }
    
  } catch (error) {
    console.error('商家登录失败:', error)
    throw error
  }
}

/**
 * 获取商家工作台数据
 * @returns {Promise<Object>} 工作台数据
 */
export async function getDashboardData() {
  try {
    // return await request.get('/merchant/dashboard')
    
    await delay()
    
    console.log('获取工作台数据成功')
    return mockDashboardData
    
  } catch (error) {
    console.error('获取工作台数据失败:', error)
    throw error
  }
}

/**
 * 获取库存列表
 * @param {Object} filters - 筛选条件
 * @returns {Promise<Array>} 库存列表
 */
export async function getInventory(filters = {}) {
  try {
    // return await request.get('/merchant/inventory', { params: filters })
    
    await delay()
    
    let inventory = [...mockInventoryData]
    
    // 应用筛选条件
    if (filters.status) {
      inventory = inventory.filter(item => item.status === filters.status)
    }
    
    if (filters.brand) {
      inventory = inventory.filter(item => item.brand === filters.brand)
    }
    
    console.log('获取库存列表成功:', inventory.length, '个商品')
    return inventory
    
  } catch (error) {
    console.error('获取库存列表失败:', error)
    throw error
  }
}

/**
 * 添加库存商品
 * @param {Object} productData - 商品数据
 * @returns {Promise<Object>} 添加结果
 */
export async function addInventoryItem(productData) {
  try {
    // return await request.post('/merchant/inventory', productData)
    
    await delay()
    
    const newItem = {
      id: `inv${Date.now()}`,
      ...productData,
      createdAt: new Date().toISOString()
    }
    
    console.log('添加库存商品成功:', newItem.title)
    return newItem
    
  } catch (error) {
    console.error('添加库存商品失败:', error)
    throw error
  }
}

/**
 * 更新库存商品
 * @param {string} itemId - 商品ID
 * @param {Object} productData - 更新的商品数据
 * @returns {Promise<Object>} 更新结果
 */
export async function updateInventoryItem(itemId, productData) {
  try {
    // return await request.put(`/merchant/inventory/${itemId}`, productData)
    
    await delay()
    
    const updatedItem = {
      id: itemId,
      ...productData,
      updatedAt: new Date().toISOString()
    }
    
    console.log('更新库存商品成功:', itemId)
    return updatedItem
    
  } catch (error) {
    console.error('更新库存商品失败:', error)
    throw error
  }
}

/**
 * 删除库存商品
 * @param {string} itemId - 商品ID
 * @returns {Promise<Object>} 删除结果
 */
export async function deleteInventoryItem(itemId) {
  try {
    // return await request.delete(`/merchant/inventory/${itemId}`)
    
    await delay()
    
    console.log('删除库存商品成功:', itemId)
    return { deleted: true }
    
  } catch (error) {
    console.error('删除库存商品失败:', error)
    throw error
  }
}

/**
 * 获取销售统计数据
 * @param {string} period - 时间周期
 * @returns {Promise<Object>} 统计数据
 */
export async function getSalesStats(period = '7d') {
  try {
    // return await request.get('/merchant/stats/sales', { params: { period } })

    await delay()

    const stats = mockSalesStats[period] || mockSalesStats['7d']

    console.log('获取销售统计成功:', period)
    return stats

  } catch (error) {
    console.error('获取销售统计失败:', error)
    throw error
  }
}

/**
 * 获取数据分析统计数据（用于MerchantProfile页面）
 * @param {string} period - 时间周期 ('7d', '30d', '90d')
 * @returns {Promise<Object>} 完整的分析数据
 */
export async function getStatsData(period = '7d') {
  try {
    // return await request.get('/merchant/stats/analysis', { params: { period } })

    await delay()

    const stats = mockSalesStats[period] || mockSalesStats['7d']

    console.log('获取数据分析统计成功:', period)
    return stats

  } catch (error) {
    console.error('获取数据分析统计失败:', error)
    throw error
  }
}

/**
 * 获取库存统计数据
 * @returns {Promise<Object>} 库存统计
 */
export async function getInventoryStats() {
  try {
    // return await request.get('/merchant/stats/inventory')
    
    await delay()
    
    const stats = {
      brandDistribution: {
        labels: ['苹果', '华为', '小米', 'OPPO', '其他'],
        data: [45, 25, 15, 10, 5]
      },
      topSelling: [
        { name: 'iPhone 14 Pro', count: 8, rank: 1 },
        { name: '小米 13 Ultra', count: 6, rank: 2 },
        { name: '华为 P50 Pro', count: 5, rank: 3 },
        { name: 'iPhone 13', count: 4, rank: 4 },
        { name: 'OPPO Find X5', count: 3, rank: 5 }
      ]
    }
    
    console.log('获取库存统计成功')
    return stats
    
  } catch (error) {
    console.error('获取库存统计失败:', error)
    throw error
  }
}

/**
 * 获取商家信息
 * @returns {Promise<Object>} 商家信息
 */
export async function getMerchantProfile() {
  try {
    // return await request.get('/merchant/profile')
    
    await delay()
    
    console.log('获取商家信息成功')
    return mockMerchantProfile
    
  } catch (error) {
    console.error('获取商家信息失败:', error)
    throw error
  }
}

/**
 * 更新商家信息
 * @param {Object} profileData - 商家信息
 * @returns {Promise<Object>} 更新结果
 */
export async function updateMerchantProfile(profileData) {
  try {
    // return await request.put('/merchant/profile', profileData)

    await delay()

    const updatedProfile = {
      ...mockMerchantProfile,
      ...profileData,
      updatedAt: new Date().toISOString()
    }

    console.log('更新商家信息成功')
    return updatedProfile

  } catch (error) {
    console.error('更新商家信息失败:', error)
    throw error
  }
}

/**
 * 标记通知为已读
 * @param {Array} notificationIds - 通知ID数组，如果为空则标记所有为已读
 * @returns {Promise<Object>} 更新结果
 */
export async function markNotificationsAsRead(notificationIds = []) {
  try {
    // return await request.post('/merchant/notifications/read', { notificationIds })

    await delay()

    console.log('标记通知为已读成功:', notificationIds.length || '全部')
    return { success: true, markedCount: notificationIds.length || mockDashboardData.unreadCount }

  } catch (error) {
    console.error('标记通知为已读失败:', error)
    throw error
  }
}
