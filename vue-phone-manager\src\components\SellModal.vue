<!-- File: src/components/SellModal.vue -->
<template>
  <div v-if="show" class="fixed inset-0 z-50 overflow-y-auto">
    <!-- 背景遮罩 -->
    <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity" @click="closeModal"></div>
    
    <!-- 模态框内容 -->
    <div class="flex min-h-full items-center justify-center p-4">
      <div class="relative bg-white rounded-xl shadow-xl w-full max-w-md transform transition-all">
        <!-- 头部 -->
        <div class="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">确认售出</h3>
          <button @click="closeModal" class="text-gray-400 hover:text-gray-600">
            <i class="fas fa-times text-xl"></i>
          </button>
        </div>

        <!-- 商品信息 -->
        <div class="p-6 border-b border-gray-200">
          <div class="flex items-center space-x-4">
            <img 
              :src="item?.image || defaultImage" 
              :alt="item?.title"
              class="w-16 h-16 rounded-lg object-cover"
            >
            <div class="flex-1">
              <h4 class="font-medium text-gray-900">{{ item?.title }}</h4>
              <p class="text-sm text-gray-500">{{ item?.brand }} | {{ item?.condition }}新</p>
              <p class="text-sm text-gray-600 mt-1">
                入库价: <span class="font-medium">¥{{ item?.purchasePrice }}</span>
              </p>
            </div>
          </div>
        </div>

        <!-- 售出表单 -->
        <form @submit.prevent="handleSubmit" class="p-6 space-y-4">
          <!-- 售出价格 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">实际售价 *</label>
            <div class="relative">
              <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">¥</span>
              <input
                v-model.number="formData.actualPrice"
                type="number"
                required
                min="0"
                step="0.01"
                :placeholder="item?.sellPrice || '0.00'"
                class="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
            </div>
            <p class="text-xs text-gray-500 mt-1">
              预期售价: ¥{{ item?.sellPrice }} | 
              利润: ¥{{ calculateProfit() }}
            </p>
          </div>

          <!-- 买家信息 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">买家信息</label>
            <input
              v-model="formData.buyerInfo"
              type="text"
              placeholder="买家姓名或联系方式（可选）"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
          </div>

          <!-- 支付方式 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">支付方式</label>
            <select
              v-model="formData.paymentMethod"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="">选择支付方式</option>
              <option value="cash">现金</option>
              <option value="wechat">微信支付</option>
              <option value="alipay">支付宝</option>
              <option value="bank">银行转账</option>
              <option value="other">其他</option>
            </select>
          </div>

          <!-- 备注 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">备注</label>
            <textarea
              v-model="formData.notes"
              rows="3"
              placeholder="售出备注信息..."
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
            ></textarea>
          </div>

          <!-- 操作按钮 -->
          <div class="flex space-x-3 pt-4">
            <button
              type="button"
              @click="closeModal"
              class="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              取消
            </button>
            <button
              type="submit"
              :disabled="loading || !formData.actualPrice"
              class="flex-1 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <i v-if="loading" class="fas fa-spinner fa-spin mr-2"></i>
              确认售出
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'

// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  item: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:show', 'confirm'])

// 响应式数据
const loading = ref(false)
const defaultImage = 'https://placehold.co/64x64/e5e7eb/9ca3af?text=无图'

// 表单数据
const formData = reactive({
  actualPrice: '',
  buyerInfo: '',
  paymentMethod: '',
  notes: ''
})

// 监听item变化，设置默认售价
watch(() => props.item, (newItem) => {
  if (newItem && newItem.sellPrice) {
    formData.actualPrice = newItem.sellPrice
  }
}, { immediate: true })

// 方法
const closeModal = () => {
  emit('update:show', false)
  resetForm()
}

const resetForm = () => {
  Object.assign(formData, {
    actualPrice: '',
    buyerInfo: '',
    paymentMethod: '',
    notes: ''
  })
}

const calculateProfit = () => {
  if (!formData.actualPrice || !props.item?.purchasePrice) return '0.00'
  const profit = formData.actualPrice - props.item.purchasePrice
  return profit.toFixed(2)
}

const handleSubmit = async () => {
  try {
    loading.value = true

    // 基本验证
    if (!formData.actualPrice || formData.actualPrice <= 0) {
      window.$app?.showNotification('请输入有效的售出价格', 'error')
      return
    }

    // 构建售出数据
    const sellData = {
      itemId: props.item.id,
      actualPrice: formData.actualPrice,
      buyerInfo: formData.buyerInfo,
      paymentMethod: formData.paymentMethod,
      notes: formData.notes,
      profit: parseFloat(calculateProfit()),
      soldAt: new Date().toISOString()
    }

    // 发送数据
    emit('confirm', sellData)
    
    window.$app?.showNotification('商品售出成功', 'success')
    closeModal()

  } catch (error) {
    console.error('售出失败:', error)
    window.$app?.showNotification('售出失败，请重试', 'error')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
/* 主色调 */
.bg-primary-500 {
  background-color: #4f46e5;
}

.text-primary-500 {
  color: #4f46e5;
}

.ring-primary-500 {
  --tw-ring-color: #4f46e5;
}

.focus\:ring-primary-500:focus {
  --tw-ring-color: #4f46e5;
}

.border-primary-500 {
  border-color: #4f46e5;
}
</style>
