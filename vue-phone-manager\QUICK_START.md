# 快速启动指南

## 🚀 项目启动

### 1. 安装依赖
```bash
npm install
```

### 2. 启动开发服务器
```bash
npm run dev
```

### 3. 访问应用
打开浏览器访问: http://localhost:3000 (或终端显示的端口)

## 📱 功能演示

### 顾客端路由
- `/customer/home` - 商品首页，可以浏览和搜索商品
- `/customer/detail/iphone14pro` - 商品详情页示例

### 商家端路由
- `/merchant/login` - 商家登录页 (PIN码: 123456)
- `/merchant/dashboard` - 商家工作台 (需要先登录)
- `/merchant/inventory` - 库存管理页 (需要先登录)
- `/merchant/profile` - 个人中心页 (需要先登录)

## 🔑 测试账号

**商家登录PIN码**: `123456`

## 🛠 开发命令

```bash
# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览生产版本
npm run preview

# 代码检查
npm run lint

# 代码格式化
npm run format
```

## 📁 项目结构

```
src/
├── api/           # API接口模块
├── assets/        # 静态资源
├── components/    # 可复用组件
├── router/        # 路由配置
├── stores/        # 状态管理
├── utils/         # 工具函数
├── views/         # 页面组件
├── App.vue        # 根组件
└── main.js        # 应用入口
```

## 🎯 核心功能

### 已实现功能
- ✅ 顾客端商品浏览和详情查看
- ✅ 商家端PIN码登录
- ✅ 商家工作台数据展示
- ✅ 库存管理页面
- ✅ 个人中心和数据统计
- ✅ 响应式设计和移动端适配
- ✅ 路由守卫和权限控制
- ✅ Mock数据支持

### 开发中功能
- 🔄 图表数据可视化
- 🔄 商品编辑和删除
- 🔄 文件上传和图片管理
- 🔄 实时数据更新

## 🔧 技术栈

- **Vue 3** - 渐进式JavaScript框架
- **Vite** - 下一代前端构建工具
- **Vue Router** - 官方路由管理器
- **Pinia** - Vue状态管理库
- **Tailwind CSS** - 原子化CSS框架
- **Axios** - HTTP客户端
- **Font Awesome** - 图标库

## 📝 注意事项

1. 项目使用Mock数据，实际部署需要配置真实API
2. 商家登录使用简单的PIN码验证，生产环境需要更安全的认证方式
3. 图片使用占位符，实际使用需要配置图片上传和存储服务
4. 部分功能仍在开发中，会显示"功能开发中"的提示

## 🐛 常见问题

### Q: 端口被占用怎么办？
A: Vite会自动尝试其他端口，查看终端输出的实际端口号

### Q: 页面显示空白怎么办？
A: 检查浏览器控制台是否有错误，确保所有依赖都已正确安装

### Q: 商家登录失败怎么办？
A: 确保输入正确的PIN码 `123456`

### Q: 如何添加新的页面？
A: 在 `src/views/` 目录下创建新组件，然后在 `src/router/index.js` 中添加路由配置

## 📞 技术支持

如有问题，请检查：
1. Node.js版本是否 >= 16
2. 依赖是否正确安装
3. 浏览器控制台是否有错误信息
