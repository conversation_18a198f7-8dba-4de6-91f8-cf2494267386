// File: src/api/customer.js
import request from '@/utils/request'

// Mock数据 - 在实际开发中，这些数据将从后端API获取
const mockProducts = [
  {
    id: 'iphone14pro',
    title: 'iPhone 14 Pro 256GB 深空黑色',
    brand: 'Apple',
    model: 'iPhone 14 Pro',
    storage: '256GB',
    color: '深空黑色',
    condition: 99,
    batteryHealth: 100,
    price: 6299,
    originalPrice: 8999,
    images: [
      'https://placehold.co/400x400/4f46e5/ffffff?text=iPhone+14+Pro+正面',
      'https://placehold.co/400x400/6366f1/ffffff?text=iPhone+14+Pro+背面',
      'https://placehold.co/400x400/8b5cf6/ffffff?text=iPhone+14+Pro+侧面',
      'https://placehold.co/400x400/a855f7/ffffff?text=包装盒+配件'
    ],
    features: [
      'A16仿生芯片，性能强劲',
      '4800万像素主摄，专业影像',
      '电池健康度100%，续航无忧',
      '原装配件齐全，品质保证'
    ],
    specs: {
      brand: 'Apple iPhone 14 Pro',
      storage: '256GB',
      color: '深空黑色',
      screenSize: '6.1英寸',
      processor: 'A16仿生芯片',
      batteryHealth: '100%',
      condition: '99新',
      network: '5G全网通',
      stockDate: '2024-06-28'
    },
    status: 'available',
    stockDays: 3,
    createdAt: '2024-06-28T10:00:00Z'
  },
  {
    id: 'huaweimate50',
    title: '华为 Mate 50 Pro 512GB 昆仑破晓',
    brand: 'Huawei',
    model: 'Mate 50 Pro',
    storage: '512GB',
    color: '昆仑破晓',
    condition: 95,
    batteryHealth: 95,
    price: 4599,
    originalPrice: 6799,
    images: [
      'https://placehold.co/400x400/f59e0b/ffffff?text=Mate+50+Pro+正面',
      'https://placehold.co/400x400/f59e0b/ffffff?text=Mate+50+Pro+背面'
    ],
    features: [
      '麒麟9000S芯片',
      '5000万像素超感知摄像头',
      '原装配件齐全',
      '7天无理由退换'
    ],
    status: 'available',
    stockDays: 15,
    createdAt: '2024-06-15T14:30:00Z'
  },
  {
    id: 'xiaomi13ultra',
    title: '小米 13 Ultra 512GB 橄榄绿',
    brand: 'Xiaomi',
    model: '13 Ultra',
    storage: '512GB',
    color: '橄榄绿',
    condition: 98,
    batteryHealth: 98,
    price: 4299,
    originalPrice: 5999,
    images: [
      'https://placehold.co/400x400/10b981/ffffff?text=小米13+Ultra+正面',
      'https://placehold.co/400x400/10b981/ffffff?text=小米13+Ultra+背面'
    ],
    features: [
      '骁龙8 Gen 2处理器',
      '徕卡影像系统',
      '专业检测认证',
      '180天质保服务'
    ],
    status: 'available',
    stockDays: 12,
    createdAt: '2024-06-20T09:15:00Z'
  },
  {
    id: 'iphone13',
    title: 'iPhone 13 128GB 红色',
    brand: 'Apple',
    model: 'iPhone 13',
    storage: '128GB',
    color: '红色',
    condition: 96,
    batteryHealth: 92,
    price: 3899,
    originalPrice: 5399,
    images: [
      'https://placehold.co/400x400/ef4444/ffffff?text=iPhone+13+正面'
    ],
    features: [
      'A15仿生芯片',
      '双摄系统',
      '热销机型',
      '品质保证'
    ],
    status: 'available',
    stockDays: 8,
    createdAt: '2024-06-22T11:20:00Z'
  },
  {
    id: 'huaweimate500',
    title: '华为 Mate 40 Pro 512GB 昆仑破晓',
    brand: 'Huawei',
    model: 'Mate 50 Pro',
    storage: '512GB',
    color: '昆仑破晓',
    condition: 95,
    batteryHealth: 95,
    price: 4599,
    originalPrice: 6799,
    images: [
      'https://placehold.co/400x400/f59e0b/ffffff?text=Mate+50+Pro+正面',
      'https://placehold.co/400x400/f59e0b/ffffff?text=Mate+50+Pro+背面'
    ],
    features: [
      '麒麟9000S芯片',
      '5000万像素超感知摄像头',
      '原装配件齐全',
      '7天无理由退换'
    ],
    status: 'available',
    stockDays: 15,
    createdAt: '2024-06-15T14:30:00Z'
  },
  {
    id: 'xiaomi13ultra',
    title: '小米 18 Ultra 512GB 橄榄绿',
    brand: 'Xiaomi',
    model: '13 Ultra',
    storage: '512GB',
    color: '橄榄绿',
    condition: 98,
    batteryHealth: 98,
    price: 4299,
    originalPrice: 5999,
    images: [
      'https://placehold.co/400x400/10b981/ffffff?text=小米13+Ultra+正面',
      'https://placehold.co/400x400/10b981/ffffff?text=小米13+Ultra+背面'
    ],
    features: [
      '骁龙8 Gen 2处理器',
      '徕卡影像系统',
      '专业检测认证',
      '180天质保服务'
    ],
    status: 'available',
    stockDays: 12,
    createdAt: '2024-06-20T09:15:00Z'
  },
  {
    id: 'xiaomi13ultra',
    title: '小米 13 Ultra 512GB 橄榄绿',
    brand: 'Xiaomi',
    model: '13 Ultra',
    storage: '512GB',
    color: '橄榄绿',
    condition: 98,
    batteryHealth: 98,
    price: 4299,
    originalPrice: 5999,
    images: [
      'https://placehold.co/400x400/10b981/ffffff?text=小米13+Ultra+正面',
      'https://placehold.co/400x400/10b981/ffffff?text=小米13+Ultra+背面'
    ],
    features: [
      '骁龙8 Gen 2处理器',
      '徕卡影像系统',
      '专业检测认证',
      '180天质保服务'
    ],
    status: 'available',
    stockDays: 12,
    createdAt: '2024-06-20T09:15:00Z'
  },
  {
    id: 'xiaomi13ultra',
    title: '小米 13 Ultra 512GB 橄榄绿',
    brand: 'Xiaomi',
    model: '13 Ultra',
    storage: '512GB',
    color: '橄榄绿',
    condition: 98,
    batteryHealth: 98,
    price: 4299,
    originalPrice: 5999,
    images: [
      'https://placehold.co/400x400/10b981/ffffff?text=小米13+Ultra+正面',
      'https://placehold.co/400x400/10b981/ffffff?text=小米13+Ultra+背面'
    ],
    features: [
      '骁龙8 Gen 2处理器',
      '徕卡影像系统',
      '专业检测认证',
      '180天质保服务'
    ],
    status: 'available',
    stockDays: 12,
    createdAt: '2024-06-20T09:15:00Z'
  },
  {
    id: 'xiaomi13ultra',
    title: '小米 13 Ultra 512GB 橄榄绿',
    brand: 'Xiaomi',
    model: '13 Ultra',
    storage: '512GB',
    color: '橄榄绿',
    condition: 98,
    batteryHealth: 98,
    price: 4299,
    originalPrice: 5999,
    images: [
      'https://placehold.co/400x400/10b981/ffffff?text=小米13+Ultra+正面',
      'https://placehold.co/400x400/10b981/ffffff?text=小米13+Ultra+背面'
    ],
    features: [
      '骁龙8 Gen 2处理器',
      '徕卡影像系统',
      '专业检测认证',
      '180天质保服务'
    ],
    status: 'available',
    stockDays: 12,
    createdAt: '2024-06-20T09:15:00Z'
  },
  {
    id: 'xiaomi13ultra',
    title: '小米 13 Ultra 512GB 橄榄绿',
    brand: 'Xiaomi',
    model: '13 Ultra',
    storage: '512GB',
    color: '橄榄绿',
    condition: 98,
    batteryHealth: 98,
    price: 4299,
    originalPrice: 5999,
    images: [
      'https://placehold.co/400x400/10b981/ffffff?text=小米13+Ultra+正面',
      'https://placehold.co/400x400/10b981/ffffff?text=小米13+Ultra+背面'
    ],
    features: [
      '骁龙8 Gen 2处理器',
      '徕卡影像系统',
      '专业检测认证',
      '180天质保服务'
    ],
    status: 'available',
    stockDays: 12,
    createdAt: '2024-06-20T09:15:00Z'
  }
]

/**
 * 模拟网络延迟
 * @param {number} ms - 延迟毫秒数
 */
const delay = (ms = 500) => new Promise(resolve => setTimeout(resolve, ms))

/**
 * 获取商品列表
 * @param {Object} filters - 筛选条件
 * @returns {Promise<Array>} 商品列表
 */
export async function getProducts(filters = {}) {
  try {
    // 在实际项目中，这里应该调用真实的API
    // return await request.get('/products', { params: filters })
    
    // 模拟API调用
    await delay()
    
    let products = [...mockProducts]
    
    // 应用筛选条件
    if (filters.brand) {
      products = products.filter(p => p.brand === filters.brand)
    }
    
    if (filters.minPrice) {
      products = products.filter(p => p.price >= filters.minPrice)
    }
    
    if (filters.maxPrice) {
      products = products.filter(p => p.price <= filters.maxPrice)
    }
    
    if (filters.status) {
      products = products.filter(p => p.status === filters.status)
    }
    
    console.log('获取商品列表成功:', products.length, '个商品')
    return products
    
  } catch (error) {
    console.error('获取商品列表失败:', error)
    throw error
  }
}

/**
 * 获取商品详情
 * @param {string} productId - 商品ID
 * @returns {Promise<Object>} 商品详情
 */
export async function getProductDetail(productId) {
  try {
    // 在实际项目中，这里应该调用真实的API
    // return await request.get(`/products/${productId}`)
    
    // 模拟API调用
    await delay()
    
    const product = mockProducts.find(p => p.id === productId)
    
    if (!product) {
      throw new Error('商品不存在')
    }
    
    console.log('获取商品详情成功:', product.title)
    return product
    
  } catch (error) {
    console.error('获取商品详情失败:', error)
    throw error
  }
}

/**
 * 搜索商品
 * @param {string} keyword - 搜索关键词
 * @param {Object} filters - 筛选条件
 * @returns {Promise<Array>} 搜索结果
 */
export async function searchProducts(keyword, filters = {}) {
  try {
    // 在实际项目中，这里应该调用真实的API
    // return await request.get('/products/search', { params: { keyword, ...filters } })
    
    // 模拟API调用
    await delay()
    
    const searchTerm = keyword.toLowerCase()
    let products = mockProducts.filter(p => 
      p.title.toLowerCase().includes(searchTerm) ||
      p.brand.toLowerCase().includes(searchTerm) ||
      p.model.toLowerCase().includes(searchTerm)
    )
    
    // 应用额外的筛选条件
    if (filters.brand) {
      products = products.filter(p => p.brand === filters.brand)
    }
    
    console.log('搜索商品成功:', keyword, '找到', products.length, '个结果')
    return products
    
  } catch (error) {
    console.error('搜索商品失败:', error)
    throw error
  }
}

/**
 * 获取热门商品
 * @param {number} limit - 限制数量
 * @returns {Promise<Array>} 热门商品列表
 */
export async function getHotProducts(limit = 4) {
  try {
    await delay()
    
    // 模拟热门商品（按价格排序）
    const hotProducts = [...mockProducts]
      .sort((a, b) => b.price - a.price)
      .slice(0, limit)
    
    console.log('获取热门商品成功:', hotProducts.length, '个商品')
    return hotProducts
    
  } catch (error) {
    console.error('获取热门商品失败:', error)
    throw error
  }
}

/**
 * 获取品牌列表
 * @returns {Promise<Array>} 品牌列表
 */
export async function getBrands() {
  try {
    await delay(200)
    
    const brands = [...new Set(mockProducts.map(p => p.brand))]
    
    console.log('获取品牌列表成功:', brands)
    return brands
    
  } catch (error) {
    console.error('获取品牌列表失败:', error)
    throw error
  }
}
